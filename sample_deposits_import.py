#!/usr/bin/env python3
"""
Sample script to create a test Excel file for deposits import functionality.
This script creates an Excel file with sample deposit data that can be used to test the import feature.
"""

import pandas as pd
from datetime import datetime, timedelta
import os

def create_sample_deposits_excel():
    """Create a sample Excel file with deposit data for testing import functionality"""
    
    # Sample data for testing
    sample_data = [
        {
            'deposit_symbol': 'DEPOSIT001',
            'principal': 10000.00,
            'interest_rate': 3.5,
            'start': '2024-01-15',
            'maturity': '2024-12-15',
            'convention': '365',
            'interest_amount': 350.00,
            'new_deposit': True,
            'liquidated': False,
            'details': 'Sample deposit 1 - 1 year term'
        },
        {
            'deposit_symbol': 'DEPOSIT002',
            'principal': 25000.00,
            'interest_rate': 4.0,
            'start': '2024-02-01',
            'maturity': '2025-02-01',
            'convention': '360',
            'interest_amount': 1000.00,
            'new_deposit': True,
            'liquidated': False,
            'details': 'Sample deposit 2 - high value'
        },
        {
            'deposit_symbol': 'DEPOSIT003',
            'principal': 5000.00,
            'interest_rate': 2.8,
            'start': '2024-03-01',
            'maturity': '2024-09-01',
            'convention': 'ACT/ACT',
            'interest_amount': 70.00,
            'new_deposit': False,
            'liquidated': True,
            'details': 'Sample deposit 3 - liquidated'
        },
        {
            'deposit_symbol': 'DEPOSIT004',
            'principal': 15000.00,
            'interest_rate': 3.8,
            'start': '2024-01-01',
            'maturity': '2024-06-30',
            'convention': '365',
            'interest_amount': 285.00,
            'new_deposit': True,
            'liquidated': False,
            'details': 'Sample deposit 4 - 6 month term'
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(sample_data)
    
    # Save to Excel file
    filename = 'sample_deposits_import.xlsx'
    df.to_excel(filename, index=False, sheet_name='Deposits')
    
    print(f"Sample Excel file created: {filename}")
    print(f"File contains {len(sample_data)} sample deposit entries")
    print("\nColumns included:")
    for col in df.columns:
        print(f"  - {col}")
    
    print(f"\nFile saved to: {os.path.abspath(filename)}")
    
    # Display sample data
    print("\nSample data preview:")
    print(df.to_string(index=False))
    
    return filename

if __name__ == "__main__":
    create_sample_deposits_excel()
