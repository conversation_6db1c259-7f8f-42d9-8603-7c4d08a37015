#!/usr/bin/env python3
"""
Test script to verify that all dropdown loading functions handle pagination correctly.
This script checks all JavaScript files for proper pagination handling in dropdown loading functions.
"""

import os
import re
from pathlib import Path

def check_pagination_handling(file_path, content):
    """Check if a JavaScript file properly handles pagination in dropdown loading functions"""
    
    # Look for functions that load dropdown options
    dropdown_functions = []
    
    # Patterns to identify dropdown loading functions
    patterns = [
        r'async function (load\w+)\s*\([^)]*\)\s*{[^}]*fetch\s*\([^)]*\)',
        r'function (load\w+)\s*\([^)]*\)\s*{[^}]*fetch\s*\([^)]*\)',
    ]
    
    for pattern in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
        for match in matches:
            function_name = match.group(1)
            function_start = match.start()
            
            # Find the end of the function (simplified - look for closing brace)
            brace_count = 0
            function_end = function_start
            in_function = False
            
            for i, char in enumerate(content[function_start:], function_start):
                if char == '{':
                    brace_count += 1
                    in_function = True
                elif char == '}':
                    brace_count -= 1
                    if in_function and brace_count == 0:
                        function_end = i
                        break
            
            function_content = content[function_start:function_end + 1]
            
            # Check if this function loads dropdown options
            if any(keyword in function_content.lower() for keyword in ['select', 'option', 'dropdown', 'appendchild']):
                dropdown_functions.append({
                    'name': function_name,
                    'content': function_content,
                    'start': function_start,
                    'end': function_end
                })
    
    return dropdown_functions

def analyze_pagination_handling(function_content):
    """Analyze if a function properly handles pagination"""
    
    # Check for pagination handling patterns
    has_while_loop = 'while (url)' in function_content or 'while(url)' in function_content
    has_next_check = 'data.next' in function_content or '.next' in function_content
    has_results_concat = 'push(...' in function_content or 'concat(' in function_content
    
    # Check for problematic patterns (only loading first page)
    only_first_page = 'data.results || data' in function_content and not has_while_loop
    
    return {
        'has_pagination': has_while_loop and has_next_check,
        'has_while_loop': has_while_loop,
        'has_next_check': has_next_check,
        'has_results_concat': has_results_concat,
        'only_first_page': only_first_page,
        'proper_handling': has_while_loop and has_next_check and has_results_concat
    }

def check_all_frontend_files():
    """Check all frontend JavaScript files for dropdown pagination handling"""
    
    print("🔍 Checking Frontend Dropdown Pagination Handling")
    print("=" * 60)
    
    # Define paths to check
    paths_to_check = [
        "frontend/js",
        "frontend_v2/js"
    ]
    
    results = {}
    
    for base_path in paths_to_check:
        if not os.path.exists(base_path):
            continue
            
        print(f"\n📁 Checking {base_path}/")
        print("-" * 40)
        
        for js_file in Path(base_path).glob("*.js"):
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                dropdown_functions = check_pagination_handling(str(js_file), content)
                
                if dropdown_functions:
                    file_results = []
                    print(f"\n📄 {js_file.name}:")
                    
                    for func in dropdown_functions:
                        analysis = analyze_pagination_handling(func['content'])
                        
                        status = "✅" if analysis['proper_handling'] else "❌"
                        if analysis['only_first_page']:
                            status = "⚠️"
                        
                        print(f"  {status} {func['name']}")
                        
                        if not analysis['proper_handling']:
                            issues = []
                            if not analysis['has_while_loop']:
                                issues.append("Missing while loop")
                            if not analysis['has_next_check']:
                                issues.append("Missing next page check")
                            if not analysis['has_results_concat']:
                                issues.append("Missing results concatenation")
                            if analysis['only_first_page']:
                                issues.append("Only loads first page")
                            
                            print(f"    Issues: {', '.join(issues)}")
                        
                        file_results.append({
                            'function': func['name'],
                            'analysis': analysis
                        })
                    
                    results[str(js_file)] = file_results
                    
            except Exception as e:
                print(f"❌ Error reading {js_file}: {str(e)}")
    
    return results

def generate_summary(results):
    """Generate a summary of the analysis"""
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    total_functions = 0
    proper_functions = 0
    problematic_functions = 0
    
    for file_path, functions in results.items():
        for func_result in functions:
            total_functions += 1
            if func_result['analysis']['proper_handling']:
                proper_functions += 1
            else:
                problematic_functions += 1
    
    print(f"Total dropdown loading functions found: {total_functions}")
    print(f"✅ Properly handling pagination: {proper_functions}")
    print(f"❌ Problematic functions: {problematic_functions}")
    
    if problematic_functions > 0:
        print(f"\n⚠️  {problematic_functions} functions need to be fixed!")
        print("\nProblematic functions:")
        for file_path, functions in results.items():
            file_has_issues = False
            for func_result in functions:
                if not func_result['analysis']['proper_handling']:
                    if not file_has_issues:
                        print(f"\n📄 {os.path.basename(file_path)}:")
                        file_has_issues = True
                    print(f"  ❌ {func_result['function']}")
    else:
        print("\n🎉 All dropdown loading functions properly handle pagination!")

def main():
    """Main function"""
    results = check_all_frontend_files()
    generate_summary(results)
    
    print("\n" + "=" * 60)
    print("✅ Analysis completed!")

if __name__ == "__main__":
    main()
