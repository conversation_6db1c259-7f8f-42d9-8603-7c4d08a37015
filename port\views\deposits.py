import pandas as pd
import logging
from rest_framework import viewsets, filters, status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticatedOrReadOnly, IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.http import HttpResponse
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill
from openpyxl.utils import get_column_letter
from rest_framework.views import APIView
from django.db import transaction
from datetime import datetime
from django.db.models import Q

from port.models import Deposits, Instrument
from port.serializers import DepositSerializer
from port.filters import DepositFilter

logger = logging.getLogger(__name__)


class DepositsViewSet(viewsets.ModelViewSet):
    queryset = Deposits.objects.all().select_related('deposit', 'deposit__currency', 'deposit__custodian').order_by('-start')
    serializer_class = DepositSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = DepositFilter
    search_fields = [
        'deposit__symbol',
        'deposit__custodian__custodian_code',
        'details',
        'convention',
    ]
    ordering_fields = [
        'start', 'maturity', 'principal', 'interest_rate',
        'deposit__symbol', 'deposit__custodian__custodian_code'
    ]
    ordering = ['-start']


class ExportDepositsExcelView(APIView):
    """
    API endpoint for exporting deposits to Excel format
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Apply all filters including search, date filters, and other filters
        queryset = DepositFilter(request.GET, queryset=Deposits.objects.all()).qs.select_related(
            'deposit', 'deposit__currency', 'deposit__custodian'
        )

        # Apply search filter if provided
        search = request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(deposit__symbol__icontains=search) |
                Q(deposit__custodian__custodian_code__icontains=search) |
                Q(details__icontains=search) |
                Q(convention__icontains=search)
            )

        # Apply ordering if provided, otherwise use default
        ordering = request.GET.get("ordering", "-start")
        if ordering:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by("-start")

        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "Deposits Export"

            # Define headers
            headers = [
                'Instrument', 'Custodian', 'Currency', 'Principal', 'Interest Rate (%)',
                'Start Date', 'Maturity Date', 'Convention', 'Interest Amount',
                'New Deposit', 'Liquidated', 'Details'
            ]

            # Add headers to first row
            for col_num, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_num, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # Add data rows
            for row_num, deposit in enumerate(queryset, 2):
                ws.cell(row=row_num, column=1, value=deposit.deposit.symbol if deposit.deposit else '')
                ws.cell(row=row_num, column=2, value=deposit.deposit.custodian.custodian_code if deposit.deposit and deposit.deposit.custodian else '')
                ws.cell(row=row_num, column=3, value=deposit.deposit.currency.currency_code if deposit.deposit and deposit.deposit.currency else '')
                ws.cell(row=row_num, column=4, value=float(deposit.principal))
                ws.cell(row=row_num, column=5, value=float(deposit.interest_rate))
                ws.cell(row=row_num, column=6, value=deposit.start.strftime('%Y-%m-%d') if deposit.start else '')
                ws.cell(row=row_num, column=7, value=deposit.maturity.strftime('%Y-%m-%d') if deposit.maturity else '')
                ws.cell(row=row_num, column=8, value=deposit.convention or '')
                ws.cell(row=row_num, column=9, value=float(deposit.interest_amount) if deposit.interest_amount else 0)
                ws.cell(row=row_num, column=10, value='Yes' if deposit.new_deposit else 'No')
                ws.cell(row=row_num, column=11, value='Yes' if deposit.liquidated else 'No')
                ws.cell(row=row_num, column=12, value=deposit.details or '')

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Create response
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="deposits_export.xlsx"'
            wb.save(response)
            return response

        except Exception as e:
            logger.error(f"Error exporting deposits to Excel: {str(e)}")
            return Response(
                {"error": f"Error exporting deposits: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ExportDepositsCsvView(APIView):
    """
    API endpoint for exporting deposits to CSV format
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Apply all filters including search, date filters, and other filters
        queryset = DepositFilter(request.GET, queryset=Deposits.objects.all()).qs.select_related(
            'deposit', 'deposit__currency', 'deposit__custodian'
        )

        # Apply search filter if provided
        search = request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(deposit__symbol__icontains=search) |
                Q(deposit__custodian__custodian_code__icontains=search) |
                Q(details__icontains=search) |
                Q(convention__icontains=search)
            )

        # Apply ordering if provided, otherwise use default
        ordering = request.GET.get("ordering", "-start")
        if ordering:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by("-start")

        try:
            # Prepare data for CSV
            data = []
            for deposit in queryset:
                data.append({
                    'Instrument': deposit.deposit.symbol if deposit.deposit else '',
                    'Custodian': deposit.deposit.custodian.custodian_code if deposit.deposit and deposit.deposit.custodian else '',
                    'Currency': deposit.deposit.currency.currency_code if deposit.deposit and deposit.deposit.currency else '',
                    'Principal': float(deposit.principal),
                    'Interest Rate (%)': float(deposit.interest_rate),
                    'Start Date': deposit.start.strftime('%Y-%m-%d') if deposit.start else '',
                    'Maturity Date': deposit.maturity.strftime('%Y-%m-%d') if deposit.maturity else '',
                    'Convention': deposit.convention or '',
                    'Interest Amount': float(deposit.interest_amount) if deposit.interest_amount else 0,
                    'New Deposit': 'Yes' if deposit.new_deposit else 'No',
                    'Liquidated': 'Yes' if deposit.liquidated else 'No',
                    'Details': deposit.details or ''
                })

            # Create DataFrame and CSV
            df = pd.DataFrame(data)

            # Create response
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="deposits_export.csv"'
            df.to_csv(response, index=False)
            return response

        except Exception as e:
            logger.error(f"Error exporting deposits to CSV: {str(e)}")
            return Response(
                {"error": f"Error exporting deposits: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DepositExcelTemplateView(APIView):
    """
    API endpoint for downloading a deposit import template Excel file
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Generate and return an Excel template for deposit imports
        """
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "Deposit Import Template"

            # Define headers
            headers = [
                'deposit_symbol', 'principal', 'interest_rate', 'start', 'maturity',
                'convention', 'interest_amount', 'new_deposit', 'liquidated', 'details'
            ]

            # Add headers to first row
            for col_num, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_num, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # Add sample data row
            sample_data = [
                'DEPOSIT001',    # deposit_symbol
                10000.00,       # principal
                3.5,            # interest_rate
                '2024-01-15',   # start
                '2024-12-15',   # maturity
                '365',          # convention
                350.00,         # interest_amount
                True,           # new_deposit
                False,          # liquidated
                'Sample deposit'  # details
            ]

            for col_num, value in enumerate(sample_data, 1):
                ws.cell(row=2, column=col_num, value=value)

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Create response
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="deposits_import_template.xlsx"'
            wb.save(response)
            return response

        except Exception as e:
            logger.error(f"Error generating deposit template: {str(e)}")
            return Response(
                {"error": f"Error generating template: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ImportDepositsExcelView(APIView):
    """
    API endpoint for importing deposits from Excel files
    """
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """
        Import deposits from Excel file
        Expected columns: deposit_symbol, principal, interest_rate, start, maturity,
        convention, interest_amount, new_deposit, liquidated, details
        """
        if 'file' not in request.FILES:
            return Response(
                {"error": "No file provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        excel_file = request.FILES['file']

        # Check file extension
        if not excel_file.name.endswith(('.xls', '.xlsx')):
            return Response(
                {"error": "File must be an Excel file (.xls or .xlsx)"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Read Excel file
            df = pd.read_excel(excel_file)

            # Validate required columns
            required_columns = [
                'deposit_symbol', 'principal', 'interest_rate', 'start', 'maturity', 'convention'
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return Response(
                    {"error": f"Missing required columns: {', '.join(missing_columns)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process data
            results = {
                'created': 0,
                'updated': 0,
                'errors': []
            }

            with transaction.atomic():
                for index, row in df.iterrows():
                    try:
                        # Parse and validate data
                        deposit_data = self._parse_row(row, index)
                        if deposit_data is None:
                            continue

                        # Create or update deposit entry
                        deposit, created = Deposits.objects.update_or_create(
                            deposit=deposit_data['deposit'],
                            maturity=deposit_data['maturity'],
                            defaults=deposit_data
                        )

                        if created:
                            results['created'] += 1
                        else:
                            results['updated'] += 1

                    except Exception as e:
                        error_msg = f"Row {index + 2}: {str(e)}"
                        results['errors'].append(error_msg)
                        logger.error(f"Error processing row {index + 2}: {str(e)}")

            return Response(results, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error importing deposits from Excel: {str(e)}")
            return Response(
                {"error": f"Error processing file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _parse_row(self, row, index):
        """Parse a single row from the Excel file"""
        try:
            # Parse dates
            if pd.isna(row['start']):
                raise ValueError("Start date is required")
            if pd.isna(row['maturity']):
                raise ValueError("Maturity date is required")

            if isinstance(row['start'], str):
                start_date = datetime.strptime(row['start'], '%Y-%m-%d').date()
            else:
                start_date = row['start'].date() if hasattr(row['start'], 'date') else row['start']

            if isinstance(row['maturity'], str):
                maturity_date = datetime.strptime(row['maturity'], '%Y-%m-%d').date()
            else:
                maturity_date = row['maturity'].date() if hasattr(row['maturity'], 'date') else row['maturity']

            # Get instrument
            instrument = Instrument.objects.filter(symbol=row['deposit_symbol']).first()
            if not instrument:
                raise ValueError(f"Instrument with symbol '{row['deposit_symbol']}' not found")

            # Parse numeric fields
            principal = float(row['principal']) if not pd.isna(row['principal']) else 0.0
            interest_rate = float(row['interest_rate']) if not pd.isna(row['interest_rate']) else 0.0
            interest_amount = float(row['interest_amount']) if 'interest_amount' in row and not pd.isna(row['interest_amount']) else None

            # Parse boolean fields
            new_deposit = bool(row['new_deposit']) if 'new_deposit' in row and not pd.isna(row['new_deposit']) else False
            liquidated = bool(row['liquidated']) if 'liquidated' in row and not pd.isna(row['liquidated']) else False

            # Validate convention
            convention = str(row['convention']) if not pd.isna(row['convention']) else '365'
            if convention not in ['360', '365', 'ACT/ACT']:
                raise ValueError(f"Invalid convention '{convention}'. Must be 360, 365, or ACT/ACT")

            return {
                'deposit': instrument,
                'principal': principal,
                'interest_rate': interest_rate,
                'start': start_date,
                'maturity': maturity_date,
                'convention': convention,
                'interest_amount': interest_amount,
                'new_deposit': new_deposit,
                'liquidated': liquidated,
                'details': str(row['details']) if 'details' in row and not pd.isna(row['details']) else ''
            }

        except Exception as e:
            error_msg = f"Row {index + 2}: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
