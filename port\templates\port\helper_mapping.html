{% extends 'base.html' %}
{% load static %}
{% load dict_extras %}

{% block title %}Helper Mapping Editor{% endblock %}

{% block extra_css %}
<style>
    .helper-mapping-container {
        max-width: 100%;
        margin: 0 auto;
    }

    .mapping-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .mapping-table th,
    .mapping-table td {
        border: 1px solid #ddd;
        padding: 12px 8px;
        text-align: left;
        min-width: 120px;
    }

    .mapping-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 10;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .mapping-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .mapping-table tbody tr:hover {
        background-color: #e3f2fd;
    }

    .mapping-table input {
        width: 100%;
        border: none;
        padding: 6px;
        background: transparent;
        font-size: 14px;
    }

    .mapping-table input:focus {
        background: #fff3cd;
        outline: 2px solid #ffc107;
        border-radius: 3px;
    }

    .table-container {
        max-height: 70vh;
        overflow: auto;
        border: 1px solid #ddd;
        border-radius: 8px;
        background: white;
    }

    .action-buttons {
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }

    .btn-group {
        margin-right: 15px;
        margin-bottom: 10px;
    }

    .btn {
        margin-right: 5px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .file-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 4px solid #2196f3;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .alert-info {
        color: #0c5460;
        background-color: #d1ecf1;
        border-color: #bee5eb;
    }

    .loading {
        display: none;
        text-align: center;
        padding: 30px;
        font-size: 18px;
        color: #666;
    }

    .row-actions {
        white-space: nowrap;
        text-align: center;
    }

    .row-actions button {
        margin: 0 2px;
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 4px;
    }

    .row-number {
        background-color: #f1f3f4;
        font-weight: bold;
        text-align: center;
        color: #5f6368;
        width: 50px;
    }

    .add-row-section {
        margin: 20px 0;
        padding: 15px;
        background: #f0f8ff;
        border-radius: 8px;
        border: 2px dashed #007bff;
    }

    .stats-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background: #e8f5e8;
        border-radius: 6px;
    }

    .stats-info .badge {
        font-size: 14px;
        padding: 6px 12px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid helper-mapping-container">
    <div class="row">
        <div class="col-12">
            <h1><i class="fas fa-table"></i> Helper Mapping Editor</h1>

            <div class="file-info">
                <div class="row">
                    <div class="col-md-8">
                        <strong><i class="fas fa-file-excel"></i> File:</strong> {{ file_path }}<br>
                        <strong><i class="fas fa-info-circle"></i> Status:</strong>
                        {% if file_exists %}
                            <span class="text-success"><i class="fas fa-check-circle"></i> File exists</span>
                        {% else %}
                            <span class="text-danger"><i class="fas fa-exclamation-circle"></i> File not found</span>
                        {% endif %}
                    </div>
                    <div class="col-md-4 text-right">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>

            {% if error %}
                <div class="alert alert-danger">
                    <strong><i class="fas fa-exclamation-triangle"></i> Error:</strong> {{ error }}
                    <div class="mt-2">
                        <button type="button" class="btn btn-primary btn-sm" onclick="createSampleFile()">
                            <i class="fas fa-plus"></i> Create Sample File
                        </button>
                    </div>
                </div>
            {% endif %}

            <div id="message-container"></div>

            {% if helper_mapping_data %}
                <div class="stats-info">
                    <div>
                        <span class="badge badge-primary">
                            <i class="fas fa-table"></i> {{ helper_mapping_data.shape.0 }} rows
                        </span>
                        <span class="badge badge-info">
                            <i class="fas fa-columns"></i> {{ helper_mapping_data.shape.1 }} columns
                        </span>
                    </div>
                    <div>
                        <small class="text-muted">Last modified: <span id="last-modified">Unknown</span></small>
                    </div>
                </div>

                <div class="action-buttons">
                    <div class="btn-group">
                        <button type="button" class="btn btn-success" onclick="saveData()">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportData()">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="addRow()">
                            <i class="fas fa-plus"></i> Add Row
                        </button>
                        <button type="button" class="btn btn-warning" onclick="addColumn()">
                            <i class="fas fa-plus"></i> Add Column
                        </button>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-danger" onclick="clearAllData()">
                            <i class="fas fa-trash"></i> Clear All
                        </button>
                    </div>
                </div>

                <div class="loading" id="loading">
                    <i class="fas fa-spinner fa-spin"></i> Processing...
                </div>

                <div class="add-row-section">
                    <h5><i class="fas fa-plus-circle"></i> Quick Add Row</h5>
                    <div class="row" id="quick-add-form">
                        {% for column in helper_mapping_data.columns %}
                            <div class="col-md-2 mb-2">
                                <label class="form-label small">{{ column }}</label>
                                <input type="text" class="form-control form-control-sm"
                                       id="new-{{ column|slugify }}" placeholder="Enter {{ column }}">
                            </div>
                        {% endfor %}
                        <div class="col-12 mt-2">
                            <button type="button" class="btn btn-success btn-sm" onclick="addRowFromForm()">
                                <i class="fas fa-plus"></i> Add Row
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearForm()">
                                <i class="fas fa-eraser"></i> Clear Form
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <table class="mapping-table" id="mapping-table">
                        <thead>
                            <tr>
                                <th class="row-number">#</th>
                                {% for column in helper_mapping_data.columns %}
                                    <th>
                                        <div>{{ column }}</div>
                                        <small class="text-light">Column {{ forloop.counter }}</small>
                                    </th>
                                {% endfor %}
                                <th style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="mapping-tbody">
                            {% for row in helper_mapping_data.data %}
                                <tr data-row-index="{{ forloop.counter0 }}">
                                    <td class="row-number">{{ forloop.counter }}</td>
                                    {% for column in helper_mapping_data.columns %}
                                        <td>
                                            <input type="text"
                                                   value="{{ row|lookup:column }}"
                                                   onchange="updateCellValue({{ forloop.parentloop.counter0 }}, '{{ column }}', this.value)"
                                                   class="cell-input"
                                                   placeholder="Enter {{ column }}">
                                        </td>
                                    {% endfor %}
                                    <td class="row-actions">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="duplicateRow({{ forloop.counter0 }})" title="Duplicate">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteRow({{ forloop.counter0 }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let helperMappingData = {{ helper_mapping_json|safe }};
let hasUnsavedChanges = false;

// Cell value updates
function updateCellValue(rowIndex, columnName, value) {
    if (!helperMappingData.data[rowIndex]) {
        helperMappingData.data[rowIndex] = {};
    }

    helperMappingData.data[rowIndex][columnName] = value;
    hasUnsavedChanges = true;
    updateSaveButtonState();
}

// Add new row
function addRow() {
    const newRow = {};
    helperMappingData.columns.forEach(col => {
        newRow[col] = '';
    });

    helperMappingData.data.push(newRow);
    refreshTable();
    hasUnsavedChanges = true;
    updateSaveButtonState();
}

// Add row from form
function addRowFromForm() {
    const newRow = {};
    let hasData = false;

    helperMappingData.columns.forEach(col => {
        const input = document.getElementById('new-' + col.toLowerCase().replace(/[^a-z0-9]/g, '-'));
        const value = input ? input.value.trim() : '';
        newRow[col] = value;
        if (value) hasData = true;
    });

    if (!hasData) {
        showMessage('error', 'Please enter at least one value');
        return;
    }

    helperMappingData.data.push(newRow);
    refreshTable();
    clearForm();
    hasUnsavedChanges = true;
    updateSaveButtonState();
    showMessage('success', 'Row added successfully');
}

// Clear form
function clearForm() {
    helperMappingData.columns.forEach(col => {
        const input = document.getElementById('new-' + col.toLowerCase().replace(/[^a-z0-9]/g, '-'));
        if (input) input.value = '';
    });
}

// Duplicate row
function duplicateRow(rowIndex) {
    if (!helperMappingData.data[rowIndex]) return;

    const originalRow = helperMappingData.data[rowIndex];
    const newRow = { ...originalRow };

    helperMappingData.data.splice(rowIndex + 1, 0, newRow);
    refreshTable();
    hasUnsavedChanges = true;
    updateSaveButtonState();
    showMessage('success', 'Row duplicated successfully');
}

// Add new column
function addColumn() {
    const columnName = prompt('Enter column name:');
    if (!columnName) return;

    helperMappingData.columns.push(columnName);
    helperMappingData.data.forEach(row => {
        row[columnName] = '';
    });

    location.reload(); // Reload to rebuild the form and table
}

// Delete row
function deleteRow(rowIndex) {
    if (!confirm('Are you sure you want to delete this row?')) return;

    helperMappingData.data.splice(rowIndex, 1);
    refreshTable();
    hasUnsavedChanges = true;
    updateSaveButtonState();
    showMessage('success', 'Row deleted successfully');
}

// Clear all data
function clearAllData() {
    if (!confirm('Are you sure you want to clear all data? This cannot be undone.')) return;

    helperMappingData.data = [];
    refreshTable();
    hasUnsavedChanges = true;
    updateSaveButtonState();
    showMessage('success', 'All data cleared');
}

// Save data
function saveData() {
    showLoading(true);

    fetch('{% url "helper_mapping_api" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            sheet_data: helperMappingData.data
        })
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        if (data.success) {
            showMessage('success', data.message);
            hasUnsavedChanges = false;
            updateSaveButtonState();
            updateLastModified();
        } else {
            showMessage('error', 'Error: ' + data.error);
        }
    })
    .catch(error => {
        showLoading(false);
        showMessage('error', 'Network error: ' + error.message);
    });
}

// Refresh table display
function refreshTable() {
    const tbody = document.getElementById('mapping-tbody');
    tbody.innerHTML = '';

    helperMappingData.data.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.setAttribute('data-row-index', index);

        // Row number
        const rowNumTd = document.createElement('td');
        rowNumTd.className = 'row-number';
        rowNumTd.textContent = index + 1;
        tr.appendChild(rowNumTd);

        // Data cells
        helperMappingData.columns.forEach(column => {
            const td = document.createElement('td');
            const input = document.createElement('input');
            input.type = 'text';
            input.value = row[column] || '';
            input.className = 'cell-input';
            input.placeholder = 'Enter ' + column;
            input.onchange = function() {
                updateCellValue(index, column, this.value);
            };
            td.appendChild(input);
            tr.appendChild(td);
        });

        // Actions cell
        const actionsTd = document.createElement('td');
        actionsTd.className = 'row-actions';
        actionsTd.innerHTML = `
            <button type="button" class="btn btn-sm btn-outline-primary"
                    onclick="duplicateRow(${index})" title="Duplicate">
                <i class="fas fa-copy"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger"
                    onclick="deleteRow(${index})" title="Delete">
                <i class="fas fa-trash"></i>
            </button>
        `;
        tr.appendChild(actionsTd);

        tbody.appendChild(tr);
    });

    updateStats();
}

// Update statistics
function updateStats() {
    const rowCount = helperMappingData.data.length;
    const colCount = helperMappingData.columns.length;

    document.querySelector('.badge:first-child').innerHTML =
        `<i class="fas fa-table"></i> ${rowCount} rows`;
    document.querySelector('.badge:nth-child(2)').innerHTML =
        `<i class="fas fa-columns"></i> ${colCount} columns`;
}

// Utility functions
function showLoading(show) {
    document.getElementById('loading').style.display = show ? 'block' : 'none';
}

function showMessage(type, message) {
    const container = document.getElementById('message-container');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';

    container.innerHTML = `
        <div class="alert ${alertClass}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
            ${message}
            <button type="button" class="close" onclick="this.parentElement.remove()">
                <span>&times;</span>
            </button>
        </div>
    `;

    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) alert.remove();
        }, 5000);
    }
}

function updateSaveButtonState() {
    const saveButton = document.querySelector('[onclick="saveData()"]');
    if (saveButton) {
        if (hasUnsavedChanges) {
            saveButton.classList.remove('btn-success');
            saveButton.classList.add('btn-warning');
            saveButton.innerHTML = '<i class="fas fa-save"></i> Save Changes *';
        } else {
            saveButton.classList.remove('btn-warning');
            saveButton.classList.add('btn-success');
            saveButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
        }
    }
}

function updateLastModified() {
    document.getElementById('last-modified').textContent = new Date().toLocaleString();
}

function refreshData() {
    if (hasUnsavedChanges) {
        if (!confirm('You have unsaved changes. Are you sure you want to refresh?')) {
            return;
        }
    }
    location.reload();
}

function exportData() {
    // Convert data to CSV and download
    let csv = helperMappingData.columns.join(',') + '\n';

    helperMappingData.data.forEach(row => {
        const values = helperMappingData.columns.map(col => {
            const value = row[col] || '';
            return '"' + value.toString().replace(/"/g, '""') + '"';
        });
        csv += values.join(',') + '\n';
    });

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'helper_mapping.csv';
    a.click();
    window.URL.revokeObjectURL(url);

    showMessage('success', 'Data exported successfully');
}

function createSampleFile() {
    if (confirm('This will create a sample helper_mapping.xlsx file. Continue?')) {
        // You could make an AJAX call to create the sample file
        showMessage('info', 'Feature not implemented yet. Please use the Django management command: python manage.py create_helper_mapping');
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateLastModified();
    updateStats();
});

// Warn user about unsaved changes
window.addEventListener('beforeunload', function(e) {
    if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
    }
});
</script>
{% endblock %}
