#!/usr/bin/env python3
"""
Comprehensive tests to validate that the refactored code from brokers/management 
to port/services produces the same outputs.

This test suite compares the old and new implementations to ensure data integrity
and functional equivalence after the refactoring.
"""

import os
import sys
import json
import pandas as pd
import tempfile
import shutil
from datetime import datetime, date
from unittest.mock import patch, MagicMock, mock_open
from django.test import TestCase, override_settings
from django.core.management import call_command
from django.conf import settings

# Import the new services
from port.services.provider.bnr_service import BnrService
from port.services.provider.ibkr_service import IbkrService
from port.services.provider.tdv_service import TdvService

# Import models for testing
from port.models import Bnr, Currency, Instrument, Portfolio, Journal


class RefactoringValidationTestCase(TestCase):
    """Base test case for refactoring validation tests"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_data_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.test_data_dir)
        
        # Create sample test data
        self.sample_bnr_xml = """<?xml version="1.0" encoding="utf-8"?>
        <DataSet>
            <Body>
                <Cube>
                    <Rate currency="USD">4.9500</Rate>
                    <Rate currency="EUR">5.4500</Rate>
                </Cube>
            </Body>
        </DataSet>"""
        
        self.sample_ibkr_data = {
            "OpenPositions": [
                {"symbol": "AAPL", "position": "100", "markPrice": "150.00"},
                {"symbol": "GOOGL", "position": "50", "markPrice": "2500.00"}
            ],
            "CashReport": [
                {"currency": "USD", "total": "10000.00"},
                {"currency": "EUR", "total": "5000.00"}
            ]
        }
        
        self.sample_tdv_data = {
            "portof": [
                {"symbol": "TLV", "sold": 1000, "pret": "15.50"},
                {"symbol": "BRD", "sold": 500, "pret": "12.30"}
            ],
            "activitate": [
                {"symbol": "TLV", "cantitate": 100, "pret": "15.00", "data": "2024-01-15"},
                {"symbol": "BRD", "cantitate": 50, "pret": "12.00", "data": "2024-01-16"}
            ]
        }


class BnrServiceValidationTest(RefactoringValidationTestCase):
    """Test BNR service refactoring validation"""
    
    @patch('port.services.provider.bnr_service.requests')
    @patch('port.services.provider.bnr_service.xmltodict')
    def test_bnr_service_output_consistency(self, mock_xmltodict, mock_requests):
        """Test that BNR service produces consistent output after refactoring"""
        
        # Mock the API response
        mock_response = MagicMock()
        mock_response.content = self.sample_bnr_xml.encode()
        mock_requests.get.return_value = mock_response
        
        # Mock xmltodict parsing
        mock_data = {
            'Rate': [
                {'@currency': 'USD', '#text': '4.9500'},
                {'@currency': 'EUR', '#text': '5.4500'}
            ]
        }
        mock_xmltodict.parse.return_value = {'DataSet': {'Body': {'Cube': mock_data}}}
        
        # Test the new service
        result = BnrService.get_last_10_days_currency()
        
        # Validate structure and content
        self.assertIsInstance(result, dict)
        self.assertIn('Rate', result)
        
        # Test data storage
        with patch('port.services.storage.bnr_storage.BnrStorageService') as mock_storage:
            with patch('port.services.excel.bnr_excel_service.BnrExcelService') as mock_excel:
                mock_df = pd.DataFrame([
                    {'currency': 'USD', 'rate': 4.95, 'date': date.today()},
                    {'currency': 'EUR', 'rate': 5.45, 'date': date.today()}
                ])
                mock_excel.process_bnr_data.return_value = mock_df
                
                BnrService.store_bnr_data_in_db()
                
                # Verify the service calls
                mock_excel.process_bnr_data.assert_called_once()
                mock_storage.store_currency_from_df.assert_called_once()
                mock_storage.store_bnr_data_from_df.assert_called_once()


class IbkrServiceValidationTest(RefactoringValidationTestCase):
    """Test IBKR service refactoring validation"""
    
    @patch('port.services.provider.ibkr_service.IbkrExcelService')
    def test_ibkr_service_data_processing(self, mock_excel_service):
        """Test that IBKR service processes data consistently"""
        
        # Mock the Excel service to return sample DataFrames
        mock_excel_service.get_dataframes_from_statements.return_value = {
            'OpenPositions': pd.DataFrame(self.sample_ibkr_data['OpenPositions']),
            'CashReport': pd.DataFrame(self.sample_ibkr_data['CashReport'])
        }
        
        # Mock all storage services
        with patch('port.services.storage.ibkr.ibkr_instrument_storage.IbkrInstrumentStorageService') as mock_instrument, \
             patch('port.services.storage.ibkr.ibkr_portfolio_storage.IbkrPortfolioStorageService') as mock_portfolio, \
             patch('port.services.storage.ibkr.ibkr_cash_storage.IbkrCashStorageService') as mock_cash, \
             patch('port.services.storage.ibkr.ibkr_journal_storage.IbkrJournalStorage') as mock_journal:
            
            # Test the service
            IbkrService.store_ibkr_data_in_db(self.sample_ibkr_data)
            
            # Verify all storage services were called
            mock_instrument.store_instruments_from_security_info_df.assert_called()
            mock_portfolio.store_portfolio.assert_called()
            mock_cash.store_cash_from_cash_report_df.assert_called()
            mock_journal.store_journals.assert_called()
    
    def test_ibkr_file_operations(self):
        """Test IBKR file operations consistency"""
        
        # Test directory creation
        with patch('os.makedirs') as mock_makedirs:
            IbkrService.ensure_directories_exist()
            mock_makedirs.assert_called_once()
        
        # Test file saving
        test_reports = {"test": "data"}
        with patch('builtins.open', mock_open()) as mock_file:
            with patch('json.dump') as mock_json_dump:
                IbkrService.save_financial_statements(test_reports)
                mock_file.assert_called_once()
                mock_json_dump.assert_called_once_with(test_reports, mock_file.return_value.__enter__.return_value)


class TdvServiceValidationTest(RefactoringValidationTestCase):
    """Test TDV service refactoring validation"""
    
    @patch('port.services.provider.nch_extractor_service.NchExtractorService')
    def test_tdv_service_data_extraction(self, mock_extractor):
        """Test that TDV service extracts data consistently"""
        
        # Mock the extractor service
        mock_extractor.get_tdv_table_data.side_effect = [
            self.sample_tdv_data['portof'],
            self.sample_tdv_data['activitate']
        ]
        
        # Test data extraction
        portof_df = TdvService.get_json_extracted_data_for_table("portof", "2024-01-15")
        activitate_df = TdvService.get_json_extracted_data_for_table("activitate", "2024-01-15")
        
        # Validate DataFrames
        self.assertIsInstance(portof_df, pd.DataFrame)
        self.assertIsInstance(activitate_df, pd.DataFrame)
        self.assertEqual(len(portof_df), 2)
        self.assertEqual(len(activitate_df), 2)
        
        # Verify extractor calls
        self.assertEqual(mock_extractor.get_tdv_table_data.call_count, 2)
    
    @patch('port.services.provider.nch_extractor_service.NchExtractorService')
    def test_tdv_service_full_workflow(self, mock_extractor):
        """Test TDV service full workflow consistency"""
        
        # Mock the extractor service
        mock_extractor.get_tdv_table_data.side_effect = [
            self.sample_tdv_data['portof'],
            self.sample_tdv_data['activitate']
        ]
        
        # Mock all storage services
        with patch('port.services.storage.tdv.tdv_storage.TDVStorageService') as mock_storage, \
             patch('port.services.storage.tdv.tdv_instrument_storage.TDVInstrumentStorage') as mock_instrument, \
             patch('port.services.storage.tdv.tdv_portfolio_storage.TDVPortfolioStorageService') as mock_portfolio, \
             patch('port.services.storage.tdv.tdv_journal_storage.TDVJournalStorageService') as mock_journal:
            
            # Test the full workflow
            TdvService.store_tdv_data_in_db("2024-01-15")
            
            # Verify all storage services were called
            mock_storage.store_portof_in_db.assert_called_once()
            mock_storage.store_activitate_in_db.assert_called_once()
            mock_instrument.store_instruments_from_portof.assert_called_once()
            mock_portfolio.store_portof_in_db_from_portof.assert_called_once()
            mock_journal.store_journals.assert_called_once()


class DataIntegrityValidationTest(RefactoringValidationTestCase):
    """Test data integrity after refactoring"""
    
    def test_data_structure_consistency(self):
        """Test that data structures remain consistent"""
        
        # Test DataFrame creation from sample data
        portof_df = pd.DataFrame(self.sample_tdv_data['portof'])
        self.assertIn('symbol', portof_df.columns)
        self.assertIn('sold', portof_df.columns)
        self.assertIn('pret', portof_df.columns)
        
        cash_df = pd.DataFrame(self.sample_ibkr_data['CashReport'])
        self.assertIn('currency', cash_df.columns)
        self.assertIn('total', cash_df.columns)
    
    def test_file_path_consistency(self):
        """Test that file paths are consistent after refactoring"""
        
        # Test IBKR paths
        expected_ibkr_path = os.path.join(settings.FILE_ROOT, 'ibkr', 'raw')
        self.assertEqual(IbkrService.STATEMENTS_PATH, expected_ibkr_path)
        
        # Test date formatting consistency
        today = datetime.today().date()
        self.assertIsInstance(today, date)


class ManagementCommandValidationTest(RefactoringValidationTestCase):
    """Test management command equivalence"""
    
    @patch('port.services.provider.bnr_service.BnrService.store_bnr_data_in_db')
    def test_bnr_management_command(self, mock_store):
        """Test that BNR management command works with new service"""
        
        # Test the management command
        call_command('curs_bnr')
        
        # Verify the service was called
        mock_store.assert_called_once()
    
    def test_management_command_structure(self):
        """Test that management commands maintain proper structure"""
        
        # Import the command
        from port.management.commands.curs_bnr import Command
        
        # Verify it's a proper Django command
        self.assertTrue(hasattr(Command, 'handle'))
        
        # Test command instantiation
        command = Command()
        self.assertIsNotNone(command)


class PerformanceValidationTest(RefactoringValidationTestCase):
    """Test performance characteristics after refactoring"""
    
    def test_service_initialization_performance(self):
        """Test that services initialize efficiently"""
        
        import time
        
        # Test BNR service
        start_time = time.time()
        BnrService()
        bnr_time = time.time() - start_time
        
        # Test IBKR service
        start_time = time.time()
        IbkrService()
        ibkr_time = time.time() - start_time
        
        # Test TDV service
        start_time = time.time()
        TdvService()
        tdv_time = time.time() - start_time
        
        # Verify reasonable initialization times (should be very fast)
        self.assertLess(bnr_time, 1.0)  # Less than 1 second
        self.assertLess(ibkr_time, 1.0)
        self.assertLess(tdv_time, 1.0)


class ErrorHandlingValidationTest(RefactoringValidationTestCase):
    """Test error handling consistency after refactoring"""
    
    @patch('port.services.provider.bnr_service.requests')
    def test_bnr_service_error_handling(self, mock_requests):
        """Test BNR service error handling"""
        
        # Test network error
        mock_requests.get.side_effect = Exception("Network error")
        
        with self.assertRaises(Exception):
            BnrService.get_last_10_days_currency()
    
    def test_file_operation_error_handling(self):
        """Test file operation error handling"""
        
        # Test invalid file path
        with patch('builtins.open', side_effect=FileNotFoundError("File not found")):
            with self.assertRaises(FileNotFoundError):
                IbkrService.save_financial_statements({"test": "data"})


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    if not settings.configured:
        settings.configure(
            DEBUG=True,
            DATABASES={
                'default': {
                    'ENGINE': 'django.db.backends.sqlite3',
                    'NAME': ':memory:',
                }
            },
            INSTALLED_APPS=[
                'django.contrib.auth',
                'django.contrib.contenttypes',
                'port',
            ],
            FILE_ROOT='/tmp/test_files/',
        )
    
    django.setup()
    
    # Run the tests
    from django.test.runner import DiscoverRunner
    test_runner = DiscoverRunner(verbosity=2)
    failures = test_runner.run_tests(['__main__'])
    
    if failures:
        sys.exit(1)
    else:
        print("\n✅ All refactoring validation tests passed!")
