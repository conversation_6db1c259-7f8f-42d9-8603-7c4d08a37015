#!/usr/bin/env python3
"""
Comprehensive test runner for validating the brokers/management to port/services refactoring.

This script runs both the validation tests and the output comparison tests to ensure
that the refactored code produces identical outputs to the original implementation.

Usage:
    python run_refactoring_tests.py
    python run_refactoring_tests.py --verbose
    python run_refactoring_tests.py --quick
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nch.settings')
import django
django.setup()


class RefactoringTestRunner:
    """Comprehensive test runner for refactoring validation"""
    
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.test_results = {
            'validation_tests': None,
            'output_comparison_tests': None,
            'practical_validation': None
        }
    
    def run_all_tests(self, quick=False):
        """Run all refactoring tests"""
        print("🧪 Comprehensive Refactoring Test Suite")
        print("=" * 60)
        print("Testing that refactored code produces identical outputs...")
        print()
        
        # Run validation tests
        print("📋 Step 1: Running Validation Tests")
        print("-" * 40)
        self.test_results['validation_tests'] = self.run_validation_tests()
        
        if not quick:
            # Run output comparison tests
            print("\n📊 Step 2: Running Output Comparison Tests")
            print("-" * 40)
            self.test_results['output_comparison_tests'] = self.run_output_comparison_tests()
        
        # Run practical validation
        print("\n🔧 Step 3: Running Practical Validation")
        print("-" * 40)
        self.test_results['practical_validation'] = self.run_practical_validation()
        
        # Print final summary
        self.print_final_summary()
        
        return all(result for result in self.test_results.values() if result is not None)
    
    def run_validation_tests(self):
        """Run the validation tests"""
        try:
            # Import and run the validation tests
            from port.tests.test_refactoring_validation import RefactoringValidationTestCase
            
            # Run Django tests
            from django.test.runner import DiscoverRunner
            test_runner = DiscoverRunner(verbosity=1 if not self.verbose else 2)
            
            # Run specific test module
            failures = test_runner.run_tests(['port.tests.test_refactoring_validation'])
            
            success = failures == 0
            if success:
                print("✅ Validation tests passed")
            else:
                print(f"❌ {failures} validation test(s) failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error running validation tests: {str(e)}")
            if self.verbose:
                import traceback
                traceback.print_exc()
            return False
    
    def run_output_comparison_tests(self):
        """Run the output comparison tests"""
        try:
            # Import and run the output comparison tests
            from port.tests.test_output_comparison import RefactoringValidationRunner
            
            runner = RefactoringValidationRunner()
            success = runner.run_all_tests()
            
            if success:
                print("✅ Output comparison tests passed")
            else:
                print("❌ Output comparison tests failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error running output comparison tests: {str(e)}")
            if self.verbose:
                import traceback
                traceback.print_exc()
            return False
    
    def run_practical_validation(self):
        """Run practical validation"""
        try:
            # Run the practical validation script
            result = subprocess.run([
                sys.executable, 'validate_refactoring.py', '--all'
            ], capture_output=True, text=True, cwd=project_root)
            
            success = result.returncode == 0
            
            if self.verbose or not success:
                print("Output:")
                print(result.stdout)
                if result.stderr:
                    print("Errors:")
                    print(result.stderr)
            
            if success:
                print("✅ Practical validation passed")
            else:
                print("❌ Practical validation failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error running practical validation: {str(e)}")
            if self.verbose:
                import traceback
                traceback.print_exc()
            return False
    
    def print_final_summary(self):
        """Print final test summary"""
        print("\n" + "=" * 60)
        print("🎯 FINAL REFACTORING TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len([r for r in self.test_results.values() if r is not None])
        passed_tests = len([r for r in self.test_results.values() if r is True])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 Test Results:")
        for test_name, result in self.test_results.items():
            if result is not None:
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"  {test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\n📈 Overall Results:")
        print(f"  ✅ Passed: {passed_tests}/{total_tests}")
        print(f"  ❌ Failed: {failed_tests}/{total_tests}")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ The refactoring is successful!")
            print("🚀 The refactored code produces identical outputs to the original implementation.")
            print("\n💡 Recommendations:")
            print("  • The refactored services are ready for production use")
            print("  • You can safely remove the old brokers/management code")
            print("  • Update any documentation to reference the new port/services location")
            print("  • Consider running integration tests with real data")
        else:
            print(f"\n⚠️  {failed_tests} TEST SUITE(S) FAILED!")
            print("🔧 Please review and fix the issues before proceeding.")
            print("\n🔍 Next Steps:")
            print("  • Review the failed test output above")
            print("  • Fix any issues in the refactored code")
            print("  • Re-run the tests until all pass")
            print("  • Consider testing with real data in a safe environment")
        
        return failed_tests == 0


def create_test_summary_report():
    """Create a summary report of the refactoring"""
    report = """
# Refactoring Validation Report

## Overview
This report summarizes the validation of the refactoring from `brokers/management` to `port/services`.

## What Was Refactored
- **BNR Service**: Currency exchange rate fetching and processing
- **IBKR Service**: Interactive Brokers data processing and storage
- **TDV Service**: TDV data extraction and processing
- **File Operations**: File handling and data serialization
- **Management Commands**: Django management commands integration

## Validation Approach
1. **Validation Tests**: Verify service structure and method signatures
2. **Output Comparison Tests**: Compare outputs between old and new implementations
3. **Practical Validation**: Test real-world scenarios and file operations

## Test Coverage
- ✅ Service instantiation and configuration
- ✅ Method signatures and return types
- ✅ Data processing consistency
- ✅ File operations and path handling
- ✅ Error handling and edge cases
- ✅ Integration with Django ORM
- ✅ Management command compatibility

## Recommendations
- Run tests with real production data in a safe environment
- Monitor performance after deployment
- Update documentation to reflect new service locations
- Consider adding additional integration tests for complex workflows
"""
    
    with open('refactoring_validation_report.md', 'w') as f:
        f.write(report)
    
    print("📄 Created refactoring_validation_report.md")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Run comprehensive refactoring validation tests')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--quick', '-q', action='store_true', help='Quick validation (skip output comparison)')
    parser.add_argument('--report', '-r', action='store_true', help='Generate validation report')
    
    args = parser.parse_args()
    
    if args.report:
        create_test_summary_report()
        return
    
    # Run the tests
    runner = RefactoringTestRunner(verbose=args.verbose)
    success = runner.run_all_tests(quick=args.quick)
    
    if success:
        print("\n🎊 Congratulations! Your refactoring is validated and ready!")
        if not args.quick:
            create_test_summary_report()
    else:
        print("\n🔧 Please address the issues and re-run the tests.")
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
