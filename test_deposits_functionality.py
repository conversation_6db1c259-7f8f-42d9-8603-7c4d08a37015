#!/usr/bin/env python3
"""
Test script to verify that deposits functionality is working correctly.
This script tests the deposits API endpoints and functionality.
"""

import requests
import os
from urllib.parse import urlencode

# Configuration
BASE_URL = "http://localhost:8000"
TOKEN = "your_access_token_here"  # Replace with actual token

def test_deposits_api():
    """Test deposits API functionality"""
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    print("🧪 Testing Deposits API Functionality")
    print("=" * 50)
    
    # Test basic deposits list
    print("\n=== Testing Deposits List ===")
    try:
        response = requests.get(f"{BASE_URL}/port/deposits/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Deposits list successful (Count: {data.get('count', 'N/A')})")
        else:
            print(f"❌ Deposits list failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Deposits list error: {str(e)}")
    
    # Test deposits with filters
    print("\n=== Testing Deposits with Filters ===")
    test_cases = [
        {
            "name": "Date range filter",
            "params": {
                "start_after": "2024-01-01",
                "maturity_before": "2024-12-31"
            }
        },
        {
            "name": "Search filter",
            "params": {
                "search": "DEPOSIT"
            }
        },
        {
            "name": "Ordering filter",
            "params": {
                "ordering": "-start"
            }
        },
        {
            "name": "Combined filters",
            "params": {
                "start_after": "2024-01-01",
                "search": "DEPOSIT",
                "ordering": "-principal"
            }
        }
    ]
    
    for test_case in test_cases:
        try:
            url = f"{BASE_URL}/port/deposits/?{urlencode(test_case['params'])}"
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {test_case['name']}: {data.get('count', 'N/A')} results")
            else:
                print(f"❌ {test_case['name']}: {response.status_code}")
        except Exception as e:
            print(f"❌ {test_case['name']}: {str(e)}")

def test_deposits_exports():
    """Test deposits export functionality"""
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    print("\n=== Testing Deposits Exports ===")
    
    # Test Excel export
    try:
        response = requests.get(f"{BASE_URL}/port/deposits/export/excel/", headers=headers)
        if response.status_code == 200:
            print(f"✅ Excel export successful (Size: {len(response.content)} bytes)")
        else:
            print(f"❌ Excel export failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Excel export error: {str(e)}")
    
    # Test CSV export
    try:
        response = requests.get(f"{BASE_URL}/port/deposits/export/csv/", headers=headers)
        if response.status_code == 200:
            print(f"✅ CSV export successful (Size: {len(response.content)} bytes)")
        else:
            print(f"❌ CSV export failed: {response.status_code}")
    except Exception as e:
        print(f"❌ CSV export error: {str(e)}")
    
    # Test template download
    try:
        response = requests.get(f"{BASE_URL}/port/deposits/template/excel/", headers=headers)
        if response.status_code == 200:
            print(f"✅ Template download successful (Size: {len(response.content)} bytes)")
        else:
            print(f"❌ Template download failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Template download error: {str(e)}")

def test_deposits_crud():
    """Test deposits CRUD operations"""
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    print("\n=== Testing Deposits CRUD Operations ===")
    
    # Test create deposit (this would require valid instrument ID)
    print("ℹ️  CRUD operations require valid instrument data in the database")
    print("ℹ️  These tests should be run with actual data")

def check_frontend_files():
    """Check if frontend files are properly updated"""
    
    print("\n=== Checking Frontend Files ===")
    
    files_to_check = [
        "frontend/views/deposites.html",
        "frontend/js/deposites.js",
        "frontend_v2/views/deposites.html",
        "frontend_v2/js/deposites.js"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for key features
            features = {
                "Search functionality": "searchInput" in content,
                "Date filters": "dateFrom" in content and "dateTo" in content,
                "Export buttons": "exportExcelBtn" in content,
                "Import functionality": "importExcelInput" in content,
                "Sorting": "sort-link" in content,
                "Pagination": "paginationContainer" in content
            }
            
            print(f"\n📁 {file_path}:")
            for feature, present in features.items():
                status = "✅" if present else "❌"
                print(f"  {status} {feature}")
        else:
            print(f"❌ {file_path}: File not found")

if __name__ == "__main__":
    if TOKEN == "your_access_token_here":
        print("⚠️  Please update the TOKEN variable with your actual access token")
        print("   You can get this from your browser's localStorage or by logging in")
        print("\n🔍 Checking frontend files instead...")
        check_frontend_files()
    else:
        test_deposits_api()
        test_deposits_exports()
        test_deposits_crud()
        check_frontend_files()
    
    print("\n✅ Test completed!")
