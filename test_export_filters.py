#!/usr/bin/env python3
"""
Test script to verify that journal export filters are working correctly.
This script tests the export functionality with various filter combinations.
"""

import requests
import os
from urllib.parse import urlencode

# Configuration
BASE_URL = "http://localhost:8000"
TOKEN = "your_access_token_here"  # Replace with actual token

def test_export_with_filters():
    """Test export functionality with different filter combinations"""
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    # Test cases with different filter combinations
    test_cases = [
        {
            "name": "Date range only",
            "params": {
                "date_after": "2024-01-01",
                "date_before": "2024-12-31"
            }
        },
        {
            "name": "Search only",
            "params": {
                "search": "AAPL"
            }
        },
        {
            "name": "Date range + Search",
            "params": {
                "date_after": "2024-01-01",
                "date_before": "2024-12-31",
                "search": "AAPL"
            }
        },
        {
            "name": "Date range + Search + Ordering",
            "params": {
                "date_after": "2024-01-01",
                "date_before": "2024-12-31",
                "search": "AAPL",
                "ordering": "-date"
            }
        },
        {
            "name": "All filters",
            "params": {
                "date_after": "2024-01-01",
                "date_before": "2024-12-31",
                "search": "AAPL",
                "ordering": "-date",
                "custodian": "1",
                "storno": "false"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== Testing: {test_case['name']} ===")
        
        # Test Excel export
        excel_url = f"{BASE_URL}/port/journals/export/excel/?{urlencode(test_case['params'])}"
        print(f"Excel URL: {excel_url}")
        
        try:
            response = requests.get(excel_url, headers=headers)
            if response.status_code == 200:
                print(f"✅ Excel export successful (Content-Length: {len(response.content)} bytes)")
            else:
                print(f"❌ Excel export failed: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Excel export error: {str(e)}")
        
        # Test DBF export
        dbf_url = f"{BASE_URL}/port/journals/export/dbf/?{urlencode(test_case['params'])}"
        print(f"DBF URL: {dbf_url}")
        
        try:
            response = requests.get(dbf_url, headers=headers)
            if response.status_code == 200:
                print(f"✅ DBF export successful (Content-Length: {len(response.content)} bytes)")
            else:
                print(f"❌ DBF export failed: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ DBF export error: {str(e)}")

def test_template_download():
    """Test template download functionality"""
    print("\n=== Testing Template Download ===")
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    template_url = f"{BASE_URL}/port/journals/template/excel/"
    print(f"Template URL: {template_url}")
    
    try:
        response = requests.get(template_url, headers=headers)
        if response.status_code == 200:
            print(f"✅ Template download successful (Content-Length: {len(response.content)} bytes)")
            
            # Save template for inspection
            with open("downloaded_template.xlsx", "wb") as f:
                f.write(response.content)
            print("📁 Template saved as 'downloaded_template.xlsx'")
        else:
            print(f"❌ Template download failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Template download error: {str(e)}")

if __name__ == "__main__":
    print("🧪 Testing Journal Export Filters")
    print("=" * 50)
    
    if TOKEN == "your_access_token_here":
        print("⚠️  Please update the TOKEN variable with your actual access token")
        print("   You can get this from your browser's localStorage or by logging in")
    else:
        test_template_download()
        test_export_with_filters()
    
    print("\n✅ Test completed!")
