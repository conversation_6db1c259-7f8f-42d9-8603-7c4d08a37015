#!/usr/bin/env python3
"""
Test script to verify that the accrual calculation buttons work correctly.
This script tests both the backend API endpoints and frontend integration.
"""

import requests
import os
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
TOKEN = "your_access_token_here"  # Replace with actual token

def test_accrual_endpoints():
    """Test the accrual calculation API endpoints"""
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    print("🧪 Testing Accrual Calculation Endpoints")
    print("=" * 50)
    
    # Test bond accruals endpoint
    print("\n=== Testing Bond Accruals ===")
    try:
        response = requests.post(f"{BASE_URL}/port/bond-accruals/", headers=headers, json={})
        if response.status_code in [200, 201, 204]:
            data = response.json() if response.content else {}
            print(f"✅ Bond accruals endpoint working (Status: {response.status_code})")
            if data.get('message'):
                print(f"   Message: {data['message']}")
        else:
            print(f"❌ Bond accruals endpoint failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Bond accruals endpoint error: {str(e)}")
    
    # Test deposits accruals endpoint
    print("\n=== Testing Deposits Accruals ===")
    try:
        response = requests.post(f"{BASE_URL}/port/deposits/calculate-accruals/", headers=headers, json={})
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"✅ Deposits accruals endpoint working (Status: {response.status_code})")
            if data.get('message'):
                print(f"   Message: {data['message']}")
            if data.get('ubo_used'):
                print(f"   UBO used: {data['ubo_used']}")
        else:
            print(f"❌ Deposits accruals endpoint failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Deposits accruals endpoint error: {str(e)}")

def check_frontend_integration():
    """Check if frontend files have the accrual buttons properly integrated"""
    
    print("\n=== Checking Frontend Integration ===")
    
    files_to_check = [
        ("frontend/views/journals.html", "Frontend HTML"),
        ("frontend_v2/views/journals.html", "Frontend v2 HTML"),
        ("frontend/js/journals.js", "Frontend JavaScript"),
        ("frontend_v2/js/journals.js", "Frontend v2 JavaScript")
    ]
    
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for required elements
            checks = {}
            
            if file_path.endswith('.html'):
                checks = {
                    "Bond accruals button": "calculateBondAccrualsBtn" in content,
                    "Deposit accruals button": "calculateDepositAccrualsBtn" in content,
                    "Accrual section": "Calculare Acumulări" in content,
                    "Bootstrap icons": "bi-calculator" in content and "bi-bank" in content
                }
            elif file_path.endswith('.js'):
                checks = {
                    "Bond accruals event listener": "calculateBondAccrualsBtn" in content and "addEventListener" in content,
                    "Deposit accruals event listener": "calculateDepositAccrualsBtn" in content and "addEventListener" in content,
                    "Bond accruals handler": "handleCalculateBondAccruals" in content,
                    "Deposit accruals handler": "handleCalculateDepositAccruals" in content,
                    "Bond API endpoint": "/port/bond-accruals/" in content,
                    "Deposits API endpoint": "/port/deposits/calculate-accruals/" in content
                }
            
            print(f"\n📄 {description}:")
            all_passed = True
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
                if not passed:
                    all_passed = False
            
            if all_passed:
                print(f"  🎉 All checks passed for {description}")
        else:
            print(f"❌ {file_path}: File not found")

def check_backend_integration():
    """Check if backend files have the accrual endpoints properly integrated"""
    
    print("\n=== Checking Backend Integration ===")
    
    files_to_check = [
        ("port/views/deposits.py", "Deposits Views"),
        ("port/urls.py", "URL Configuration")
    ]
    
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = {}
            
            if "deposits.py" in file_path:
                checks = {
                    "DepositsService import": "from port.services.provider.deposits_service import DepositsService" in content,
                    "Action decorator import": "from rest_framework.decorators import action" in content,
                    "Calculate accruals action": "@action(detail=False, methods=['post'], url_path='calculate-accruals')" in content,
                    "Calculate accruals method": "def calculate_accruals(self, request):" in content,
                    "DepositsService usage": "DepositsService(ubo=ubo)" in content
                }
            elif "urls.py" in file_path:
                checks = {
                    "Deposits accruals URL": "deposits/calculate-accruals/" in content,
                    "Bond accruals URL": "bond-accruals/" in content,
                    "DepositsViewSet import": "DepositsViewSet" in content
                }
            
            print(f"\n📄 {description}:")
            all_passed = True
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
                if not passed:
                    all_passed = False
            
            if all_passed:
                print(f"  🎉 All checks passed for {description}")
        else:
            print(f"❌ {file_path}: File not found")

def main():
    """Main function"""
    if TOKEN == "your_access_token_here":
        print("⚠️  Please update the TOKEN variable with your actual access token")
        print("   You can get this from your browser's localStorage or by logging in")
        print("\n🔍 Checking integration without API calls...")
        check_frontend_integration()
        check_backend_integration()
    else:
        test_accrual_endpoints()
        check_frontend_integration()
        check_backend_integration()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
    print("\n📋 Summary:")
    print("   - Added accrual calculation buttons to journals interface")
    print("   - Bond accruals: Uses existing /port/bond-accruals/ endpoint")
    print("   - Deposits accruals: Uses new /port/deposits/calculate-accruals/ endpoint")
    print("   - Both buttons show loading states and success/error messages")
    print("   - Journals table refreshes automatically after calculations")

if __name__ == "__main__":
    main()
