# Generated by Django 4.2.13 on 2025-04-07 16:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('port', '0005_alter_cashreport_unique_together'),
    ]

    operations = [
        migrations.CreateModel(
            name='Trade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(max_length=50, unique=True)),
                ('operation', models.CharField(max_length=50)),
                ('symbol', models.CharField(max_length=50)),
                ('date', models.DateField()),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=20)),
                ('value', models.DecimalField(decimal_places=2, max_digits=20)),
                ('profit', models.DecimalField(decimal_places=2, max_digits=20)),
                ('isin', models.Char<PERSON>ield(blank=True, max_length=12, null=True)),
                ('details', models.TextField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='port.currency')),
            ],
        ),
    ]
