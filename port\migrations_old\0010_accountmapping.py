# Generated by Django 4.2.13 on 2025-05-25 16:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('port', '0009_error_rename_report_date_corporateaction_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('main_account', models.Char<PERSON>ield(max_length=50)),
                ('account_saga', models.Char<PERSON>ield(blank=True, max_length=50, null=True, unique=True)),
                ('account_type', models.CharField(choices=[('credit', 'credit'), ('debit', 'debit')], max_length=10)),
            ],
        ),
    ]
