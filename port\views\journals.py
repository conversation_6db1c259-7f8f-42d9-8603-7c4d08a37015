import dbf
import logging
import pandas as pd
from rest_framework import viewsets, filters, status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.response import Response
from port.models import AccountMapping, Error, Journal, Ubo, Custodian, Account, Operation, Partner, Instrument
from port.serializers import JournalCalculatedSerializer
from port.filters import JournalFilter
from django_filters.rest_framework import DjangoFilterBackend
from django.utils.text import slugify
from rest_framework.permissions import IsAuthenticatedOrReadOnly, IsAuthenticated
from django.http import HttpResponse
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill
from openpyxl.utils import get_column_letter
from rest_framework.views import APIView
from port.helpers import AccountsHelper
from django.db import transaction
from datetime import datetime

logger = logging.getLogger(__name__)

class JournalViewSet(viewsets.ModelViewSet):
    search_fields = [
        "transactionid",
        "details",
        "operation__operation_code",
        "partner__partner_code",
        "custodian__custodian_code",
        "account__account_code",
        "instrument__symbol",
        "instrument__currency__currency_code",
        "ubo__ubo_code",
    ]
    permission_classes = [IsAuthenticatedOrReadOnly]
    queryset = Journal.objects.all().select_related(
        "ubo", "custodian", "account", "operation", "partner", "instrument"
    )
    serializer_class = JournalCalculatedSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_class = JournalFilter
    ordering_fields = "__all__"
    ordering = ["-date"]


class ExportJournalsExcelView(APIView, AccountsHelper):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Apply all filters including search, date filters, and other filters
        queryset = JournalFilter(request.GET, queryset=Journal.objects.all()).qs.select_related(
            "ubo", "custodian", "account", "operation__debit", "operation__credit",
            "partner", "instrument__currency", "account__currency", "custodian__custodian_type"
        )

        # Apply search filter if provided
        search = request.GET.get("search")
        if search:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(transactionid__icontains=search) |
                Q(details__icontains=search) |
                Q(operation__operation_code__icontains=search) |
                Q(partner__partner_code__icontains=search) |
                Q(custodian__custodian_code__icontains=search) |
                Q(account__account_code__icontains=search) |
                Q(instrument__symbol__icontains=search) |
                Q(instrument__currency__currency_code__icontains=search) |
                Q(ubo__ubo_code__icontains=search)
            )

        # Apply ordering if provided, otherwise use default
        ordering = request.GET.get("ordering", "date")
        if ordering:
            queryset = queryset.order_by(ordering, "custodian__custodian_code", "transactionid")
        else:
            queryset = queryset.order_by("date", "custodian__custodian_code", "transactionid")

        wb = Workbook()
        ws = wb.active
        ws.title = "Jurnal"

        headers = [
            'Firma', 'Banca', 'Cont', 'Operatie', 'Contraparte', 'Instrument',
            'Valoare', 'Valoare Deviza', 'Cantitate', 'Storno', '.', 'Nr.inreg.',
            'Tip inregistrare', 'Jurnal', 'Data', 'Data scadenta', 'Numar document', 
            'Cod tip factura', 'Cont debit simbol', 'Cont debit titlu', '',
            '', '', '', 'Cont credit simbol', 'Cont credit titlu', '', '', '', '',
            'Explicatie', 'Valoare', 'Cod Partener', 'Partener CIF', 'Partener Nume',
            'Partener Rezidenta', 'Partener Judet', 'Partener Cont', 'Angajat CNP', 
            'Angajat Nume', 'Angajat Cont', 'Optiune TVA', 'Cota TVA', 'Cod TVA SAF-T',
            'Moneda', 'Curs BNR', 'Valoare deviza', 'Stornare - Nr. inreg.',
            'Incasari/plati', 'Diferente curs', 'TVA la incasare', 'Colectare/Deducere TVA', 
            'Efect de incasat/platit', 'Banca efect', 'Centre de cost', 'Informatii export', 
            'Punct de lucru', 'Deductibilitate', 'Reevaluare', 'Factura simplificata', 
            'Borderou de achizitie', 'arnet prod. Agricole', 'Contract', 'Document stornat'
        ]

        header_style = Font(bold=True)
        header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_style
            cell.fill = header_fill
            ws.column_dimensions[get_column_letter(col)].width = max(len(str(header)) + 2, 12)

        for row_num, record in enumerate(queryset, start=2):
            custodian_code = record.custodian.custodian_code + (record.account.custodian_detail or '')
            currency_account = record.account.currency.currency_code
            partner = record.partner.partner_code
            symbol = record.instrument.symbol
            
            debit_analitic = self.build_analitic(
                account_code=record.operation.debit.account_code, 
                currency=currency_account,
                custodian_detail=custodian_code,
                partner=partner,
                symbol=symbol,
                credit=False
            )
            credit_analitic = self.build_analitic(
                record.operation.credit.account_code,
                currency_account,
                custodian_code,
                partner,
                symbol,
                credit=True
            )

            row_data = [
                record.ubo.ubo_code,
                record.custodian.custodian_code,
                record.account.account_code,
                record.operation.operation_code,
                record.partner.partner_code,
                record.instrument.symbol,
                record.value_ron,
                record.value,
                record.quantity,
                "x" if record.storno else "",
                ".",
                record.id,
                record.custodian.custodian_type.partner_type_code,
                record.custodian.custodian_type.journal_code,
                record.date.strftime("%Y%m%d"),
                record.date.strftime("%Y%m%d"),
                record.transactionid,
                "",
                debit_analitic,
                record.operation.debit.account_name,
                '', '', '', '',
                credit_analitic,
                record.operation.credit.account_name,
                '', '', '', '',
                record.details,
                abs(record.value_ron) if not record.storno else -abs(record.value_ron),
                '', '', '', '', '', '', '', '', '',
                'Neimpozabile', '0.00', '',
                currency_account,
                record.bnr,
                abs(record.value) if not record.storno else -abs(record.value),
                '', '', '', '0', '', '', '', '', 
                '********-********-CielStd_ASV', 'Sediu', '', '', '0', '0', '0', '0', '0'
            ]

            for col_num, value in enumerate(row_data, 1):
                ws.cell(row=row_num, column=col_num, value=value)

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="jurnal_export.xlsx"'
        wb.save(response)
        return response


class ExportJournalsDbfView(APIView, AccountsHelper):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Apply all filters including search, date filters, and other filters
        queryset = JournalFilter(request.GET, queryset=Journal.objects.all()).qs.select_related(
            "ubo", "custodian", "account", "operation__debit", "operation__credit",
            "partner", "instrument__currency", "account__currency"
        )

        # Apply search filter if provided
        search = request.GET.get("search")
        if search:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(transactionid__icontains=search) |
                Q(details__icontains=search) |
                Q(operation__operation_code__icontains=search) |
                Q(partner__partner_code__icontains=search) |
                Q(custodian__custodian_code__icontains=search) |
                Q(account__account_code__icontains=search) |
                Q(instrument__symbol__icontains=search) |
                Q(instrument__currency__currency_code__icontains=search) |
                Q(ubo__ubo_code__icontains=search)
            )

        # Apply ordering if provided, otherwise use default
        ordering = request.GET.get("ordering", "date")
        if ordering:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by("date")

        first_date = queryset.first().date.strftime("%d-%m-%Y") if queryset.exists() else "empty"
        last_date = queryset.last().date.strftime("%d-%m-%Y") if queryset.exists() else "empty"
        filename = f"NC_{first_date}_{last_date}"

        table_path = f"/tmp/{slugify(filename)}.dbf"
        table = dbf.Table(
            table_path,
            """
                NDP C(16);
                CONT_D C(20);
                CONT_C C(20);
                SUMA N(15,2);
                COD_VALUTA C(3);
                CURS N(15,4);
                SUMA_VAL N(14,2);
                DATA D;
                EXPLICATIE C(48);
                GRUPA C(16);
                NR_DOC C(16)
            """
        )
        table.open(mode=dbf.READ_WRITE)

        for record in queryset:
            debit = self.build_analitic(
                account_code=record.operation.debit.account_code,
                currency=record.account.currency.currency_code,
                custodian_detail=record.custodian.custodian_code + (record.account.custodian_detail or ''),
                partner=record.partner.partner_code,
                symbol=record.instrument.symbol,
                credit=False
            )
            credit = self.build_analitic(
                account_code=record.operation.credit.account_code,
                currency=record.account.currency.currency_code,
                custodian_detail=record.custodian.custodian_code + (record.account.custodian_detail or ''),
                partner=record.partner.partner_code,
                symbol=record.instrument.symbol,
                credit=True
            )
            debit_account = AccountMapping.objects.filter(main_account=debit).first()
            credit_account = AccountMapping.objects.filter(main_account=credit).first()
            
            if debit_account and credit_account:
                debit_account = debit_account.account_saga
                credit_account = credit_account.account_saga
            else:
                Error.objects.create(
                    logger_name="ExportJournalsDbfView",
                    level="ERROR",
                    message=f"Missing account mapping for {debit} or {credit} for accounts {debit} and {credit}",
                    trace=""
                )
                logger.error(f"Missing account mapping for {debit} or {credit} for accounts {debit} and {credit}")
                continue

            suma = float(record.value_ron) if record.value_ron else 0.0
            curs = float(record.bnr) if record.bnr else 0.0

            table.append((
                str(record.id),
                str(debit_account),
                str(credit_account),
                abs(suma) if not record.storno else -abs(suma),
                str(record.account.currency.currency_code),
                curs,
                abs(record.value) if not record.storno else -abs(record.value),
                record.date,
                str(record.operation.operation_code),
                "",
                str(record.id),
            ))

        table.close()

        with open(table_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename={filename}.dbf'

        return response


class ImportJournalsExcelView(APIView):
    """
    API endpoint for importing journals from Excel files
    """
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """
        Import journals from Excel file
        Expected columns: date, transactionid, ubo_code, custodian_code, account_code,
        operation_code, partner_code, symbol, value, value_ron, bnr, quantity, details, storno
        """
        if 'file' not in request.FILES:
            return Response(
                {"error": "No file provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        excel_file = request.FILES['file']

        # Check file extension
        if not excel_file.name.endswith(('.xls', '.xlsx')):
            return Response(
                {"error": "File must be an Excel file (.xls or .xlsx)"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Read Excel file
            df = pd.read_excel(excel_file)

            # Validate required columns
            required_columns = [
                'date', 'transactionid', 'ubo_code', 'custodian_code',
                'account_code', 'operation_code', 'partner_code', 'symbol',
                'value', 'quantity', 'details'
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return Response(
                    {"error": f"Missing required columns: {', '.join(missing_columns)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process data
            results = {
                'created': 0,
                'updated': 0,
                'errors': []
            }

            with transaction.atomic():
                for index, row in df.iterrows():
                    try:
                        # Parse and validate data
                        journal_data = self._parse_row(row, index)
                        if journal_data is None:
                            continue

                        # Create or update journal entry
                        journal, created = Journal.objects.update_or_create(
                            date=journal_data['date'],
                            transactionid=journal_data['transactionid'],
                            custodian=journal_data['custodian'],
                            defaults=journal_data
                        )

                        if created:
                            results['created'] += 1
                        else:
                            results['updated'] += 1

                    except Exception as e:
                        error_msg = f"Row {index + 2}: {str(e)}"
                        results['errors'].append(error_msg)
                        logger.error(f"Error processing row {index + 2}: {str(e)}")

            return Response(results, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error importing journals from Excel: {str(e)}")
            return Response(
                {"error": f"Error processing file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _parse_row(self, row, index):
        """Parse a single row from the Excel file"""
        try:
            # Parse date
            if pd.isna(row['date']):
                raise ValueError("Date is required")

            if isinstance(row['date'], str):
                date = datetime.strptime(row['date'], '%Y-%m-%d').date()
            else:
                date = row['date'].date() if hasattr(row['date'], 'date') else row['date']

            # Get foreign key objects
            ubo = Ubo.objects.filter(ubo_code=row['ubo_code']).first()
            if not ubo:
                raise ValueError(f"UBO with code '{row['ubo_code']}' not found")

            custodian = Custodian.objects.filter(custodian_code=row['custodian_code']).first()
            if not custodian:
                raise ValueError(f"Custodian with code '{row['custodian_code']}' not found")

            account = Account.objects.filter(account_code=row['account_code']).first()
            if not account:
                raise ValueError(f"Account with code '{row['account_code']}' not found")

            operation = Operation.objects.filter(operation_code=row['operation_code']).first()
            if not operation:
                raise ValueError(f"Operation with code '{row['operation_code']}' not found")

            partner = Partner.objects.filter(partner_code=row['partner_code']).first()
            if not partner:
                raise ValueError(f"Partner with code '{row['partner_code']}' not found")

            instrument = Instrument.objects.filter(symbol=row['symbol']).first()
            if not instrument:
                raise ValueError(f"Instrument with symbol '{row['symbol']}' not found")

            # Parse numeric fields
            value = float(row['value']) if not pd.isna(row['value']) else 0.0
            value_ron = float(row['value_ron']) if 'value_ron' in row and not pd.isna(row['value_ron']) else 0.0
            bnr = float(row['bnr']) if 'bnr' in row and not pd.isna(row['bnr']) else 1.0
            quantity = float(row['quantity']) if not pd.isna(row['quantity']) else 0.0

            # Parse boolean fields
            storno = bool(row['storno']) if 'storno' in row and not pd.isna(row['storno']) else False
            lock = bool(row['lock']) if 'lock' in row and not pd.isna(row['lock']) else False

            return {
                'ubo': ubo,
                'custodian': custodian,
                'account': account,
                'operation': operation,
                'partner': partner,
                'instrument': instrument,
                'date': date,
                'transactionid': str(row['transactionid']),
                'value': value,
                'value_ron': value_ron,
                'bnr': bnr,
                'quantity': quantity,
                'details': str(row['details']) if not pd.isna(row['details']) else '',
                'storno': storno,
                'lock': lock
            }

        except Exception as e:
            error_msg = f"Row {index + 2}: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)


class JournalExcelTemplateView(APIView):
    """
    API endpoint for downloading a journal import template Excel file
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Generate and return an Excel template for journal imports
        """
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "Journal Import Template"

            # Define headers
            headers = [
                'date', 'transactionid', 'ubo_code', 'custodian_code',
                'account_code', 'operation_code', 'partner_code', 'symbol',
                'value', 'value_ron', 'bnr', 'quantity', 'details', 'storno', 'lock'
            ]

            # Add headers to first row
            for col_num, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_num, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # Add sample data row
            sample_data = [
                '2024-01-15',  # date
                'TXN001',      # transactionid
                'DD',          # ubo_code
                'IBKR',        # custodian_code
                'ACC001',      # account_code
                'BUY',         # operation_code
                'PART001',     # partner_code
                'AAPL',        # symbol
                1000.00,       # value
                4500.00,       # value_ron
                4.5,           # bnr
                10,            # quantity
                'Sample transaction',  # details
                False,         # storno
                False          # lock
            ]

            for col_num, value in enumerate(sample_data, 1):
                ws.cell(row=2, column=col_num, value=value)

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Create response
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="journal_import_template.xlsx"'
            wb.save(response)
            return response

        except Exception as e:
            logger.error(f"Error generating journal template: {str(e)}")
            return Response(
                {"error": f"Error generating template: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

