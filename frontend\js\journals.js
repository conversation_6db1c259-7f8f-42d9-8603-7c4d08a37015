import { BACKEND_URL } from "./constants.js";
import { showToast } from "./utils.js";

let journalModal, journalSubmitBtn;
let editMode = false;
let editId = null;

let currentSortField = "date";
let currentSortOrder = "desc"; // or "asc"
let currentPage = 1;
let pageSize = 10;
let currentSearch = "";

export async function init() {
    journalModal = new bootstrap.Modal(document.getElementById("journalModal"));
    journalSubmitBtn = document.getElementById("submitJournalBtn");
  
    document.getElementById("addJournalBtn").addEventListener("click", async () => await openJournalForm());
    document.getElementById("journalForm").addEventListener("submit", handleJournalSubmit);
    document.getElementById("searchInput").addEventListener("input", debounce(handleSearch, 400));
    document.getElementById("dateFrom").addEventListener("change", () => loadJournals(1));
    document.getElementById("dateTo").addEventListener("change", () => loadJournals(1));
    document.getElementById("exportExcelBtn").addEventListener("click", handleExportExcel);
    document.getElementById("exportDbfBtn").addEventListener("click", handleExportDbf);
    document.getElementById("downloadTemplateBtn").addEventListener("click", handleDownloadTemplate);
    document.getElementById("importExcelInput").addEventListener("change", handleImportExcel);
    document.getElementById("calculateBondAccrualsBtn").addEventListener("click", handleCalculateBondAccruals);
    document.getElementById("calculateDepositAccrualsBtn").addEventListener("click", handleCalculateDepositAccruals);
  
    setupImportModal();
    document.getElementById("journalsTableBody").addEventListener("click", handleTableClick);
  
    document.addEventListener("click", function (e) {
      if (e.target.closest(".sort-link")) {
        e.preventDefault();
        const link = e.target.closest(".sort-link");
        const newField = link.dataset.sort;
  
        if (currentSortField === newField) {
          currentSortOrder = currentSortOrder === "asc" ? "desc" : "asc";
        } else {
          currentSortField = newField;
          currentSortOrder = "asc";
        }
  
        document.querySelectorAll(".sort-icon").forEach(icon => {
          icon.innerHTML = "";
        });
  
        const iconTarget = link.querySelector(".sort-icon");
        iconTarget.innerHTML = currentSortOrder === "asc"
          ? '<i class="bi bi-caret-up-fill"></i>'
          : '<i class="bi bi-caret-down-fill"></i>';
  
        loadJournals(1, document.getElementById("searchInput").value);
      }
    });
  
    await loadJournals();
    await loadSelectOptions();
  }
  

async function handleExportExcel() {
    const dateFrom = document.getElementById("dateFrom").value;
    const dateTo = document.getElementById("dateTo").value;
    const searchValue = document.getElementById("searchInput").value;
    const token = localStorage.getItem("accessToken");

    const params = new URLSearchParams();

    // Add all current filters that are applied to the table view
    if (dateFrom) params.append("date_after", dateFrom);
    if (dateTo) params.append("date_before", dateTo);
    if (searchValue) params.append("search", searchValue);

    // Add current sorting
    params.append("ordering", `${currentSortOrder === "desc" ? "-" : ""}${currentSortField}`);

    const url = `${BACKEND_URL}/port/journals/export/excel/?${params.toString()}`;

    try {
      const response = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) throw new Error("Exportul Excel a eșuat.");

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = "jurnal_export.xlsx";
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(downloadUrl);

      showToast("Exportul Excel a fost finalizat cu succes.", "success");
    } catch (error) {
      console.error(error);
      showToast("Eroare la exportul Excel.", "error");
    }
  }
  
  async function handleExportDbf() {
    const dateFrom = document.getElementById("dateFrom").value;
    const dateTo = document.getElementById("dateTo").value;
    const searchValue = document.getElementById("searchInput").value;
    const token = localStorage.getItem("accessToken");

    const params = new URLSearchParams();

    // Add all current filters that are applied to the table view
    if (dateFrom) params.append("date_after", dateFrom);
    if (dateTo) params.append("date_before", dateTo);
    if (searchValue) params.append("search", searchValue);

    // Add current sorting
    params.append("ordering", `${currentSortOrder === "desc" ? "-" : ""}${currentSortField}`);

    const url = `${BACKEND_URL}/port/journals/export/dbf/?${params.toString()}`;

    try {
      const response = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) throw new Error("Exportul DBF a eșuat.");

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = "jurnal_export.dbf";
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(downloadUrl);

      showToast("Exportul DBF a fost finalizat cu succes.", "success");
    } catch (error) {
      console.error(error);
      showToast("Eroare la exportul DBF.", "error");
    }
  }

  async function handleFetchIbkr() {
    try {
      const token = localStorage.getItem("accessToken");
      const res = await fetch(`${BACKEND_URL}/port/trigger-journals-ibkr-fetch/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
  
      if (!res.ok) throw new Error("Eroare la importul jurnalelor din IBKR.");
      showToast("Importul din IBKR a fost pornit.", "success");
      await loadJournals();
    } catch (err) {
      console.error(err);
      showToast("Importul din IBKR a eșuat.", "error");
    }
  }
  
  async function handleFetchTdv() {
    try {
      const token = localStorage.getItem("accessToken");
      const res = await fetch(`${BACKEND_URL}/port/trigger-journals-tdv-fetch/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
  
      if (!res.ok) throw new Error("Eroare la importul jurnalelor din TDV.");
      showToast("Importul din TDV a fost pornit.", "success");
      await loadJournals();
    } catch (err) {
      console.error(err);
      showToast("Importul din TDV a eșuat.", "error");
    }
  }
  

  function setupImportModal() {
    let importType = null;
    const importModal = new bootstrap.Modal(document.getElementById("importDateModal"));
  
    document.getElementById("fetchIbkrBtn").addEventListener("click", () => {
      importType = "ibkr";
      importModal.show();
    });
  
    document.getElementById("fetchTdvBtn").addEventListener("click", () => {
      importType = "tdv";
      importModal.show();
    });
  
    document.getElementById("confirmImportBtn").addEventListener("click", async () => {
      const importDate = document.getElementById("importDate").value;
      if (!importDate) {
        showToast("Selectează o dată pentru import.", "warning");
        return;
      }
  
      const endpoint =
        importType === "ibkr"
          ? "trigger-journals-ibkr-fetch"
          : "trigger-journals-tdv-fetch";
  
      try {
        const token = localStorage.getItem("accessToken");
        const res = await fetch(`${BACKEND_URL}/port/${endpoint}/?date=${importDate}`, {
          headers: { Authorization: `Bearer ${token}` },
        });
  
        if (!res.ok) throw new Error("Eroare la import.");
        showToast(`Importul din ${importType.toUpperCase()} a fost pornit.`, "success");
        importModal.hide();
        await loadJournals();
      } catch (err) {
        console.error(err);
        showToast(`Importul din ${importType.toUpperCase()} a eșuat.`, "error");
      }
    });
  }
  


async function loadJournals(page = 1, search = "") {
  const dateFrom = document.getElementById("dateFrom").value;
  const dateTo = document.getElementById("dateTo").value;
  const tableBody = document.getElementById("journalsTableBody");
  tableBody.innerHTML = `<tr><td colspan="20">Se încarcă...</td></tr>`;

  try {
    const token = localStorage.getItem("accessToken");
    // const url = new URL(`${BACKEND_URL}/port/journals/`);
    // url.searchParams.append("page", page);
    // url.searchParams.append("ordering", `${currentSortOrder === "desc" ? "-" : ""}${currentSortField}`);
    // if (search) url.searchParams.append("search", search);
    // if (dateFrom) url.searchParams.append("date_after", dateFrom);
    // if (dateTo) url.searchParams.append("date_before", dateTo);

    const params = new URLSearchParams();

    params.append("page", page);
    params.append("ordering", `${currentSortOrder === "desc" ? "-" : ""}${currentSortField}`);
    if (search) params.append("search", search);
    if (dateFrom) params.append("date_after", dateFrom);
    if (dateTo) params.append("date_before", dateTo);

    const url = `${BACKEND_URL}/port/journals/?${params.toString()}`;
    // const res = await fetch(url.toString(), {
      const res = await fetch(url, {
      headers: { Authorization: `Bearer ${token}` },
    });

    const json = await res.json();
    const data = json.results || [];

    tableBody.innerHTML = "";

    if (data.length === 0) {
      tableBody.innerHTML = `<tr><td colspan="20" class="text-center">Nu există înregistrări.</td></tr>`;
      return;
    }

    data.forEach(entry => {
      const row = document.createElement("tr");
      row.innerHTML = `
        <td>${entry.id}</td>
        <td>${entry.date}</td>
        <td>${entry.transactionid}</td>
        <td>${entry.ubo_code}</td>
        <td>${entry.custodian_code}</td>
        <td>${entry.account_code}</td>
        <td>${entry.operation_code}</td>
        <td>${entry.partner_code}</td>
        <td>${entry.symbol}</td>
        <td>${entry.currency_code}</td>
        <td>${entry.quantity}</td>
        <td>${entry.details}</td>
        <td>${entry.value_abs}</td>
        <td>${entry.value_ron_abs}</td>
        <td>${entry.bnr}</td>
        <td>${entry.storno ? "✔️" : ""}</td>
        <td>${entry.lock ? "✔️" : ""}</td>
        <td>${entry.debit_analitic}</td>
        <td>${entry.credit_analitic}</td>
        <td class="text-end">
          <div class="d-flex justify-content-end gap-2">
            <button class="btn btn-sm btn-warning edit-btn" data-id="${entry.id}">Editează</button>
            <button class="btn btn-sm btn-danger delete-btn" data-id="${entry.id}">Șterge</button>
          </div>
        </td>
      `;
      tableBody.appendChild(row);
    });

    updatePagination(json.count, page);
  } catch (error) {
    console.error(error);
    tableBody.innerHTML = `<tr><td colspan="20" class="text-danger">Eroare la încărcarea datelor.</td></tr>`;
  }
}

async function handleTableClick(event) {
    console.log("CLICKED")
    const id = event.target.dataset.id;
  
    if (event.target.classList.contains("edit-btn")) {
      try {
        const token = localStorage.getItem("accessToken");
        const res = await fetch(`${BACKEND_URL}/port/journals/${id}/`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        const data = await res.json();
        await openJournalForm(data);
      } catch (err) {
        showToast("Nu s-a putut deschide formularul jurnalului.", "error");
      }
    }
  
    if (event.target.classList.contains("delete-btn")) {
      if (!confirm("Ești sigur că vrei să ștergi acest jurnal?")) return;
  
      try {
        const token = localStorage.getItem("accessToken");
        const res = await fetch(`${BACKEND_URL}/port/journals/${id}/`, {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        });
  
        if (res.ok) {
          showToast("Jurnalul a fost șters.", "success");
          await loadJournals();
        } else {
          showToast("Eroare la ștergere jurnal.", "error");
        }
      } catch (err) {
        console.error(err);
        showToast("Eroare de rețea la ștergere jurnal.", "error");
      }
    }
  }
  

function updatePagination(totalCount, currentPage) {
  const paginationContainer = document.getElementById("pagination");
  const totalPages = Math.ceil(totalCount / pageSize);
  paginationContainer.innerHTML = "";

  const createPageButton = (i, label = null) => {
    const btn = document.createElement("button");
    btn.className = `btn btn-sm ${i === currentPage ? "btn-primary" : "btn-outline-primary"} px-3`;
    btn.innerText = label || i;
    btn.onclick = () => loadJournals(i, currentSearch);
    return btn;
  };

  const createEllipsis = () => {
    const span = document.createElement("span");
    span.className = "px-2";
    span.innerText = "...";
    return span;
  };

  if (currentPage > 1) {
    const prevBtn = createPageButton(currentPage - 1, "←");
    prevBtn.onclick = () => loadJournals(currentPage - 1, currentSearch);
    paginationContainer.appendChild(prevBtn);
  }

  if (totalPages <= 7) {
    for (let i = 1; i <= totalPages; i++) {
      paginationContainer.appendChild(createPageButton(i));
    }
  } else {
    paginationContainer.appendChild(createPageButton(1));
    if (currentPage > 4) paginationContainer.appendChild(createEllipsis());

    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);
    for (let i = start; i <= end; i++) {
      paginationContainer.appendChild(createPageButton(i));
    }

    if (currentPage < totalPages - 3) paginationContainer.appendChild(createEllipsis());
    paginationContainer.appendChild(createPageButton(totalPages));
  }

  if (currentPage < totalPages) {
    const nextBtn = createPageButton(currentPage + 1, "→");
    nextBtn.onclick = () => loadJournals(currentPage + 1, currentSearch);
    paginationContainer.appendChild(nextBtn);
  }
}

function debounce(fn, delay) {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => fn(...args), delay);
  };
}

function handleSearch(e) {
    currentSearch = e.target.value;
    currentPage = 1;
    loadJournals(currentPage, currentSearch);
  }

async function openJournalForm(data = null) {
  const form = document.getElementById("journalForm");
  form.reset();

  // Load select options before showing the modal
  await loadSelectOptions();

  if (data) {
    Object.keys(data).forEach(key => {
      const input = form.elements[key];
      if (input) {
        if (input.type === "checkbox") {
          input.checked = data[key];
        } else {
          input.value = data[key];
        }
      }
    });
    editMode = true;
    editId = data.id;
  } else {
    editMode = false;
    editId = null;
  }

  journalModal.show();
}

async function handleJournalSubmit(event) {
  event.preventDefault();
  const form = event.target;

  journalSubmitBtn.disabled = true;
  journalSubmitBtn.innerHTML = `<span class="spinner-border spinner-border-sm me-1"></span> Se salvează...`;

  const token = localStorage.getItem("accessToken");
  const formData = new FormData(form);
  const payload = Object.fromEntries(formData.entries());

  payload.storno = form.storno.checked;
  payload.lock = form.lock.checked;

  const method = editMode ? "PUT" : "POST";
  const url = editMode
    ? `${BACKEND_URL}/port/journals/${editId}/`
    : `${BACKEND_URL}/port/journals/`;

  try {
    const res = await fetch(url, {
      method,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(payload),
    });

    const result = await res.json();
    if (res.ok) {
      showToast("Jurnalul a fost salvat cu succes.", "success");
      await loadJournals();
      journalModal.hide();
    } else {
      showToast(result.detail || "Eroare la salvare jurnal.", "error");
    }
  } catch (err) {
    console.error(err);
    showToast("Eroare de rețea la salvare jurnal.", "error");
  } finally {
    journalSubmitBtn.disabled = false;
    journalSubmitBtn.textContent = editMode ? "Salvează" : "Adaugă";
  }
}

async function loadSelectOptions() {
  const endpoints = ["custodians", "accounts", "operations", "partners", "instruments"];

  for (const endpoint of endpoints) {
    try {
      const token = localStorage.getItem("accessToken");
      const select = document.getElementById(endpoint.slice(0, -1));

      if (select) {
        // Show loading state
        select.innerHTML = `<option value="">Se încarcă...</option>`;
        select.disabled = true;
      }

      // Handle paginated responses - load all pages
      let url = `${BACKEND_URL}/port/${endpoint}/`;
      const allItems = [];

      while (url) {
        const res = await fetch(url, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (!res.ok) throw new Error(`Eroare la încărcarea opțiunilor pentru ${endpoint}.`);
        const data = await res.json();

        allItems.push(...(data.results || data));
        url = data.next; // Move to next page if available
      }

      if (select) {
        select.innerHTML = `<option value="">-- Selectează --</option>`;
        allItems.forEach(item => {
          const option = document.createElement("option");
          option.value = item.id;
          option.textContent = item.name || item.code || item.label || item.symbol || item.custodian_code || item.account_code || item.operation_code || item.partner_code;
          select.appendChild(option);
        });
        select.disabled = false;
      }
    } catch (err) {
      console.error(`Error loading ${endpoint}:`, err);
      const select = document.getElementById(endpoint.slice(0, -1));
      if (select) {
        select.innerHTML = `<option value="">Eroare la încărcare</option>`;
        select.disabled = false;
      }
    }
  }
}

async function handleDownloadTemplate() {
  try {
    const token = localStorage.getItem("accessToken");
    const res = await fetch(`${BACKEND_URL}/port/journals/template/excel/`, {
      headers: { Authorization: `Bearer ${token}` },
    });

    if (!res.ok) throw new Error("Eroare la descărcarea template-ului.");

    const blob = await res.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "journal_import_template.xlsx";
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    showToast("Template-ul a fost descărcat cu succes.", "success");
  } catch (err) {
    console.error(err);
    showToast("Eroare la descărcarea template-ului.", "error");
  }
}

async function handleImportExcel(event) {
  const file = event.target.files[0];
  if (!file) return;

  // Reset file input
  event.target.value = '';

  // Show progress modal
  const importResultsModal = new bootstrap.Modal(document.getElementById("importResultsModal"));
  const importProgress = document.getElementById("importProgress");
  const importResults = document.getElementById("importResults");

  importProgress.classList.remove("d-none");
  importResults.classList.add("d-none");
  importResultsModal.show();

  try {
    const token = localStorage.getItem("accessToken");
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch(`${BACKEND_URL}/port/journals/import/excel/`, {
      method: "POST",
      headers: { Authorization: `Bearer ${token}` },
      body: formData,
    });

    const result = await res.json();

    // Hide progress, show results
    importProgress.classList.add("d-none");
    importResults.classList.remove("d-none");

    if (res.ok) {
      // Update counters
      document.getElementById("createdCount").textContent = result.created || 0;
      document.getElementById("updatedCount").textContent = result.updated || 0;
      document.getElementById("errorCount").textContent = result.errors?.length || 0;

      // Show errors if any
      if (result.errors && result.errors.length > 0) {
        const errorsList = document.getElementById("errorsList");
        const errorsListContent = document.getElementById("errorsListContent");

        errorsListContent.innerHTML = "";
        result.errors.forEach(error => {
          const li = document.createElement("li");
          li.textContent = error;
          errorsListContent.appendChild(li);
        });

        errorsList.classList.remove("d-none");
      } else {
        document.getElementById("errorsList").classList.add("d-none");
      }

      // Reload journals table
      await loadJournals();

      const totalProcessed = (result.created || 0) + (result.updated || 0);
      if (totalProcessed > 0) {
        showToast(`Import finalizat cu succes: ${totalProcessed} înregistrări procesate.`, "success");
      }
    } else {
      throw new Error(result.error || "Eroare la importul fișierului.");
    }
  } catch (err) {
    console.error(err);
    importProgress.classList.add("d-none");
    importResults.classList.add("d-none");
    importResultsModal.hide();
    showToast(err.message || "Eroare la importul fișierului Excel.", "error");
  }
}

async function handleCalculateBondAccruals() {
  const btn = document.getElementById("calculateBondAccrualsBtn");
  const originalText = btn.innerHTML;

  try {
    // Show loading state
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Calculează...';

    const token = localStorage.getItem("accessToken");
    const res = await fetch(`${BACKEND_URL}/port/bond-accruals/`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({})
    });

    const data = await res.json();
    if (res.ok) {
      showToast(`Acumulări obligațiuni calculate cu succes. ${data.message}`, "success");
      if (data.errors && data.errors.length) {
        console.warn("Erori:", data.errors);
        showToast(`Au fost detectate ${data.errors.length} erori. Vezi consola.`, "warning");
      }
      // Refresh journals table to show new entries
      await loadJournals();
    } else {
      showToast(`Eroare la calcularea acumulărilor: ${data.message || res.statusText}`, "error");
    }
  } catch (err) {
    console.error("Error calculating bond accruals:", err);
    showToast("Eroare de rețea la calcularea acumulărilor obligațiuni.", "error");
  } finally {
    // Restore button state
    btn.disabled = false;
    btn.innerHTML = originalText;
  }
}

async function handleCalculateDepositAccruals() {
  const btn = document.getElementById("calculateDepositAccrualsBtn");
  const originalText = btn.innerHTML;

  try {
    // Show loading state
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Calculează...';

    const token = localStorage.getItem("accessToken");
    const res = await fetch(`${BACKEND_URL}/port/deposits/calculate-accruals/`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({})
    });

    const data = await res.json();
    if (res.ok) {
      showToast(`Acumulări depozite calculate cu succes. ${data.message}`, "success");
      if (data.results && data.results.errors && data.results.errors.length) {
        console.warn("Erori:", data.results.errors);
        showToast(`Au fost detectate ${data.results.errors.length} erori. Vezi consola.`, "warning");
      }
      // Refresh journals table to show new entries
      await loadJournals();
    } else {
      showToast(`Eroare la calcularea acumulărilor: ${data.error || res.statusText}`, "error");
    }
  } catch (err) {
    console.error("Error calculating deposit accruals:", err);
    showToast("Eroare de rețea la calcularea acumulărilor depozite.", "error");
  } finally {
    // Restore button state
    btn.disabled = false;
    btn.innerHTML = originalText;
  }
}
