#!/usr/bin/env python3
"""
Sample script to create a test Excel file for journal import functionality.
This script creates an Excel file with sample journal data that can be used to test the import feature.
"""

import pandas as pd
from datetime import datetime, timedelta
import os

def create_sample_journal_excel():
    """Create a sample Excel file with journal data for testing import functionality"""
    
    # Sample data for testing
    sample_data = [
        {
            'date': '2024-01-15',
            'transactionid': 'TXN001',
            'ubo_code': 'DD',
            'custodian_code': 'IBKR',
            'account_code': 'ACC001',
            'operation_code': 'BUY',
            'partner_code': 'PART001',
            'symbol': 'AAPL',
            'value': 1000.00,
            'value_ron': 4500.00,
            'bnr': 4.5,
            'quantity': 10,
            'details': 'Sample Apple stock purchase',
            'storno': False,
            'lock': False
        },
        {
            'date': '2024-01-16',
            'transactionid': 'TXN002',
            'ubo_code': 'DD',
            'custodian_code': 'TDV',
            'account_code': 'ACC002',
            'operation_code': 'SELL',
            'partner_code': 'PART002',
            'symbol': 'MSFT',
            'value': 2000.00,
            'value_ron': 9000.00,
            'bnr': 4.5,
            'quantity': 5,
            'details': 'Sample Microsoft stock sale',
            'storno': False,
            'lock': False
        },
        {
            'date': '2024-01-17',
            'transactionid': 'TXN003',
            'ubo_code': 'DD',
            'custodian_code': 'IBKR',
            'account_code': 'ACC001',
            'operation_code': 'DIV',
            'partner_code': 'PART001',
            'symbol': 'GOOGL',
            'value': 50.00,
            'value_ron': 225.00,
            'bnr': 4.5,
            'quantity': 0,
            'details': 'Dividend payment from Google',
            'storno': False,
            'lock': False
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(sample_data)
    
    # Save to Excel file
    filename = 'sample_journal_import.xlsx'
    df.to_excel(filename, index=False, sheet_name='Journals')
    
    print(f"Sample Excel file created: {filename}")
    print(f"File contains {len(sample_data)} sample journal entries")
    print("\nColumns included:")
    for col in df.columns:
        print(f"  - {col}")
    
    print(f"\nFile saved to: {os.path.abspath(filename)}")
    
    return filename

if __name__ == "__main__":
    create_sample_journal_excel()
