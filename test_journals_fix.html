<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Journals Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 Test Journals Fix</h1>
    <p>This page tests the journals UI fix for the 404 error on instruments endpoint.</p>
    
    <div>
        <button onclick="testBackendUrl()">Test Backend URL Detection</button>
        <button onclick="testInstrumentsEndpoint()">Test Instruments Endpoint</button>
        <button onclick="testCorsSettings()">Test CORS Settings</button>
        <button onclick="testPagination()">Test Pagination</button>
    </div>
    
    <div id="results"></div>

    <script type="module">
        import { BACKEND_URL, AI_HEDGE_FUND_URL } from './frontend/js/constants.js';
        
        window.BACKEND_URL = BACKEND_URL;
        window.AI_HEDGE_FUND_URL = AI_HEDGE_FUND_URL;
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        window.testBackendUrl = function() {
            addResult(`🔍 Testing Backend URL Detection`, 'info');
            addResult(`Current hostname: ${window.location.hostname}`, 'info');
            addResult(`Detected BACKEND_URL: ${BACKEND_URL}`, 'info');
            addResult(`Detected AI_HEDGE_FUND_URL: ${AI_HEDGE_FUND_URL}`, 'info');
            
            const isCloudEnvironment = window.location.hostname === '************';
            if (isCloudEnvironment) {
                if (BACKEND_URL === 'http://************') {
                    addResult(`✅ Backend URL correctly detected for cloud environment`, 'success');
                } else {
                    addResult(`❌ Backend URL incorrect for cloud environment. Expected: http://************, Got: ${BACKEND_URL}`, 'error');
                }
            } else {
                if (BACKEND_URL === 'http://localhost:8002') {
                    addResult(`✅ Backend URL correctly detected for local environment`, 'success');
                } else {
                    addResult(`❌ Backend URL incorrect for local environment. Expected: http://localhost:8002, Got: ${BACKEND_URL}`, 'error');
                }
            }
        };
        
        window.testInstrumentsEndpoint = async function() {
            addResult(`🔍 Testing Instruments Endpoint`, 'info');
            
            try {
                const token = localStorage.getItem('accessToken');
                if (!token) {
                    addResult(`⚠️ No access token found. Please log in first.`, 'error');
                    return;
                }
                
                addResult(`Making request to: ${BACKEND_URL}/port/instruments/`, 'info');
                
                const response = await fetch(`${BACKEND_URL}/port/instruments/`, {
                    headers: { 
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                addResult(`Response status: ${response.status} ${response.statusText}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Instruments endpoint working! Found ${data.count || data.length} instruments`, 'success');
                    addResult(`Response structure: ${JSON.stringify(Object.keys(data), null, 2)}`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Instruments endpoint failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Network error: ${error.message}`, 'error');
            }
        };
        
        window.testCorsSettings = async function() {
            addResult(`🔍 Testing CORS Settings`, 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/port/instruments/`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'authorization,content-type'
                    }
                });
                
                addResult(`CORS preflight status: ${response.status}`, 'info');
                
                if (response.status === 200 || response.status === 204) {
                    addResult(`✅ CORS preflight successful`, 'success');
                } else {
                    addResult(`❌ CORS preflight failed: ${response.status}`, 'error');
                }
                
                // Check CORS headers
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                addResult(`CORS headers: ${JSON.stringify(corsHeaders, null, 2)}`, 'info');
                
            } catch (error) {
                addResult(`❌ CORS test error: ${error.message}`, 'error');
            }
        };
        
        window.testPagination = async function() {
            addResult(`🔍 Testing Pagination`, 'info');
            
            try {
                const token = localStorage.getItem('accessToken');
                if (!token) {
                    addResult(`⚠️ No access token found. Please log in first.`, 'error');
                    return;
                }
                
                // Test page 1
                addResult(`Testing page 1...`, 'info');
                const page1Response = await fetch(`${BACKEND_URL}/port/instruments/?page=1`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (page1Response.ok) {
                    const page1Data = await page1Response.json();
                    addResult(`✅ Page 1 successful: ${page1Data.results?.length || 0} items`, 'success');
                    
                    // Test page 2 if there's a next page
                    if (page1Data.next) {
                        addResult(`Testing page 2...`, 'info');
                        const page2Response = await fetch(`${BACKEND_URL}/port/instruments/?page=2`, {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });
                        
                        if (page2Response.ok) {
                            const page2Data = await page2Response.json();
                            addResult(`✅ Page 2 successful: ${page2Data.results?.length || 0} items`, 'success');
                        } else {
                            addResult(`❌ Page 2 failed: ${page2Response.status}`, 'error');
                        }
                    } else {
                        addResult(`ℹ️ No page 2 available (not enough data)`, 'info');
                    }
                } else {
                    addResult(`❌ Page 1 failed: ${page1Response.status}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Pagination test error: ${error.message}`, 'error');
            }
        };
        
        // Auto-run backend URL test
        window.testBackendUrl();
    </script>
</body>
</html>
