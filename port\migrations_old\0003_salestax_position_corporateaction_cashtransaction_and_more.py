# Generated by Django 4.2.13 on 2025-04-04 18:39

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('port', '0002_instrument_needs_to_be_checked'),
    ]

    operations = [
        migrations.CreateModel(
            name='SalesTax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(max_length=50, unique=True)),
                ('date', models.DateField()),
                ('value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('operation', models.CharField(max_length=50)),
                ('details', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='port.currency')),
            ],
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(max_length=100)),
                ('isin', models.CharField(max_length=12)),
                ('position', models.DecimalField(decimal_places=2, max_digits=15)),
                ('mark_price', models.DecimalField(decimal_places=2, max_digits=15)),
                ('position_value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('cost_basis_price', models.DecimalField(decimal_places=2, max_digits=15)),
                ('cost_basis_money', models.DecimalField(decimal_places=2, max_digits=15)),
                ('side', models.CharField(max_length=10)),
                ('accrued_int', models.DecimalField(decimal_places=2, max_digits=15)),
                ('report_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('instrument', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='port.instrument')),
            ],
        ),
        migrations.CreateModel(
            name='CorporateAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(max_length=50, unique=True)),
                ('action_type', models.CharField(max_length=50)),
                ('asset_category', models.CharField(max_length=10)),
                ('symbol', models.CharField(max_length=50)),
                ('isin', models.CharField(blank=True, max_length=12, null=True)),
                ('report_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, null=True)),
                ('proceeds', models.DecimalField(decimal_places=2, max_digits=15, null=True)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=15, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('custodian', models.CharField(default='IBKR', max_length=50)),
                ('processed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='port.currency')),
            ],
        ),
        migrations.CreateModel(
            name='CashTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(max_length=50, unique=True)),
                ('operation', models.CharField(max_length=50)),
                ('asset_category', models.CharField(blank=True, max_length=50, null=True)),
                ('symbol', models.CharField(max_length=50)),
                ('isin', models.CharField(max_length=12)),
                ('date', models.DateTimeField()),
                ('value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('details', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='port.currency')),
            ],
        ),
        migrations.CreateModel(
            name='CashReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account', models.CharField(max_length=50)),
                ('report_start_date', models.DateField()),
                ('report_end_date', models.DateField()),
                ('starting_cash', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('ending_cash', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('deposits', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('withdrawals', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('deposit_withdrawals', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('net_trades_sales', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('net_trades_purchases', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('broker_interest', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('bond_interest', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('commissions', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('other_fees', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('sales_tax', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('withholding_tax', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('fx_translation_gain_loss', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('slb_net_cash', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='port.currency')),
            ],
        ),
    ]
