# filters.py
import django_filters
from .models import Journal, Deposits

class JournalFilter(django_filters.FilterSet):
    date = django_filters.DateFromToRangeFilter()
    ubo = django_filters.NumberFilter()
    custodian = django_filters.NumberFilter()
    account = django_filters.NumberFilter()
    operation = django_filters.NumberFilter()
    instrument = django_filters.NumberFilter()
    partner = django_filters.NumberFilter()
    storno = django_filters.BooleanFilter()
    lock = django_filters.BooleanFilter()

    class Meta:
        model = Journal
        fields = [
            "ubo", "custodian", "account", "operation", "partner",
            "instrument", "storno", "lock", "date"
        ]


class DepositFilter(django_filters.FilterSet):
    start_after = django_filters.DateFilter(field_name='start', lookup_expr='gte')
    start_before = django_filters.DateFilter(field_name='start', lookup_expr='lte')
    maturity_after = django_filters.DateFilter(field_name='maturity', lookup_expr='gte')
    maturity_before = django_filters.DateFilter(field_name='maturity', lookup_expr='lte')
    deposit = django_filters.NumberFilter()
    new_deposit = django_filters.BooleanFilter()
    liquidated = django_filters.BooleanFilter()
    principal_min = django_filters.NumberFilter(field_name='principal', lookup_expr='gte')
    principal_max = django_filters.NumberFilter(field_name='principal', lookup_expr='lte')
    interest_rate_min = django_filters.NumberFilter(field_name='interest_rate', lookup_expr='gte')
    interest_rate_max = django_filters.NumberFilter(field_name='interest_rate', lookup_expr='lte')

    class Meta:
        model = Deposits
        fields = [
            'deposit', 'convention', 'new_deposit', 'liquidated',
            'start_after', 'start_before', 'maturity_after', 'maturity_before',
            'principal_min', 'principal_max', 'interest_rate_min', 'interest_rate_max'
        ]
