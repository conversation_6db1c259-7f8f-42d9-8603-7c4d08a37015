from rest_framework import viewsets, filters
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from port.models import Instrument
from port.serializers import InstrumentSerializer

class InstrumentViewSet(viewsets.ModelViewSet):
    queryset = Instrument.objects.all().select_related('currency', 'custodian').order_by('symbol')
    serializer_class = InstrumentSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['symbol', 'name', 'isin', 'custodian__custodian_code']
    ordering_fields = ['symbol', 'name', 'isin', 'custodian__custodian_code']
    ordering = ['symbol']
    filterset_fields = ['currency', 'custodian', 'type', 'sector', 'country']
